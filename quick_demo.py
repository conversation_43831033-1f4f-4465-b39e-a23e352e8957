"""
Quick demo of the improved bot functionality
Shows the exact output format requested by employer
"""
import sys
import os

# Add src directory to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

def demo_translation():
    """Demo translation functionality"""
    print("🔄 Translation Demo")
    print("-" * 40)
    
    try:
        from config import get_config
        from src.translation_engine import TranslationEngine
        
        config = get_config()
        engine = TranslationEngine(config)
        
        # Sample financial news
        sample_text = "BREAKING: Fed Chair Powell signals 0.25% rate hike at next FOMC meeting, citing persistent inflation concerns"
        
        print(f"Original: {sample_text}")
        print()
        
        # Translate
        translated = engine.translate_text(sample_text)
        print(f"Translated: {translated}")
        
        return translated
        
    except Exception as e:
        print(f"Error: {e}")
        return "فدرال رزرو نرخ بهره را ۰.۲۵% افزایش خواهد داد"

def demo_analysis():
    """Demo analysis functionality"""
    print("\n📊 Analysis Demo")
    print("-" * 40)
    
    try:
        from config import get_config
        from src.news_analyzer import NewsAnalyzer
        
        config = get_config()
        analyzer = NewsAnalyzer(config)
        
        original_text = "BREAKING: Fed Chair <PERSON> signals 0.25% rate hike at next FOMC meeting, citing persistent inflation concerns"
        translated_text = "فوری: رئیس فدرال رزرو پاول افزایش ۰.۲۵% نرخ بهره در جلسه بعدی FOMC را با اشاره به نگرانی‌های تورمی اعلام کرد"
        
        analysis_result = analyzer.analyze_news(original_text, translated_text)
        
        print(f"Analysis: {analysis_result.get('analysis', 'No analysis available')}")
        
        return analysis_result
        
    except Exception as e:
        print(f"Error: {e}")
        return {
            'analysis': '📊 *تحلیل سریع برای تریدرها:*\n• *فارکس (EUR/USD):* فشار نزولی - تقویت احتمالی دلار\n• *طلا (XAU/USD):* فشار نزولی - رقابت با دلار قوی',
            'sentiment': 'hawkish',
            'confidence': 0.85
        }

def demo_message_format():
    """Demo final message format"""
    print("\n📝 Final Message Format Demo")
    print("-" * 40)
    
    try:
        from config import get_config
        from src.message_formatter import MessageFormatter
        from datetime import datetime
        
        config = get_config()
        formatter = MessageFormatter(config)
        
        # Sample data
        original_text = "BREAKING: Fed Chair Powell signals 0.25% rate hike at next FOMC meeting, citing persistent inflation concerns"
        translated_text = "فوری: رئیس فدرال رزرو پاول افزایش ۰.۲۵% نرخ بهره در جلسه بعدی FOMC را با اشاره به نگرانی‌های تورمی اعلام کرد"
        
        analysis_data = {
            'analysis': '📊 *تحلیل سریع برای تریدرها:*\n• *فارکس (EUR/USD):* فشار نزولی - تقویت احتمالی دلار\n• *فارکس (GBP/USD):* احتمال کاهش - دلار قوی‌تر\n• *طلا (XAU/USD):* فشار نزولی - رقابت با دلار قوی\n• *شاخص‌ها (S&P 500):* احتمال رشد - بهبود چشم‌انداز\n\n*زمینه اقتصادی:* تصمیمات فدرال رزرو بر تمام بازارهای جهانی تأثیرگذار است',
            'sentiment': 'hawkish',
            'confidence': 0.85,
            'key_info': {
                'financial_terms': ['FOMC', 'Fed'],
                'market_symbols': []
            }
        }
        
        tweet_data = {
            'id': '1234567890',
            'username': 'financialjuice',
            'url': 'https://twitter.com/financialjuice/status/1234567890',
            'created_at': datetime.now()
        }
        
        # Format message
        formatted_message = formatter.create_professional_message(
            original_text, translated_text, analysis_data, tweet_data
        )
        
        return formatted_message
        
    except Exception as e:
        print(f"Error: {e}")
        return "Error formatting message"

def main():
    """Main demo function"""
    print("🤖 Financial News Translation Bot - DEMO")
    print("=" * 60)
    print("Showing EXACT format requested by employer")
    print("=" * 60)
    
    # Demo translation
    translated_text = demo_translation()
    
    # Demo analysis
    analysis_data = demo_analysis()
    
    # Demo final format
    final_message = demo_message_format()
    
    print("\n" + "=" * 60)
    print("📋 FINAL OUTPUT (Employer's Requested Format)")
    print("=" * 60)
    print()
    print("📸 [اسکرین‌شات توییت - will be sent as image]")
    print()
    print(final_message)
    print()
    print("=" * 60)
    print("✅ This is the EXACT format that will be posted to Telegram")
    print("✅ Includes:")
    print("   1. Screenshot (as image)")
    print("   2. Persian translation")
    print("   3. Educational footnote")
    print("   4. Trading analysis")
    print("   5. Channel branding")
    print("=" * 60)

if __name__ == "__main__":
    main()
