"""
Twitter Bot Module for Financial News Translation Bot
Handles Twitter monitoring WITHOUT API - Uses web scraping
"""
import requests
import asyncio
import time
import sqlite3
import re
from datetime import datetime, timedelta
from typing import List, Dict, Optional
from loguru import logger
from config import Config
from bs4 import BeautifulSoup

class TwitterBot:
    """Twitter Bot for monitoring tweets WITHOUT API - Uses FREE web scraping"""

    def __init__(self, config: Config):
        self.config = config
        self.session = requests.Session()
        self.last_tweet_id = None
        self.processed_tweets = set()
        self.setup_session()
        self.setup_database()
    
    def setup_session(self):
        """Initialize web scraping session - NO API REQUIRED"""
        try:
            # Setup headers to mimic a real browser
            self.session.headers.update({
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                'Accept-Language': 'en-US,en;q=0.5',
                'Accept-Encoding': 'gzip, deflate',
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1',
            })

            logger.info("Web scraping session initialized - NO API REQUIRED")

        except Exception as e:
            logger.error(f"Failed to setup session: {e}")
            raise
    
    def setup_database(self):
        """Setup SQLite database for tracking processed tweets"""
        try:
            conn = sqlite3.connect(self.config.DATABASE_PATH)
            cursor = conn.cursor()
            
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS processed_tweets (
                    tweet_id TEXT PRIMARY KEY,
                    username TEXT,
                    content TEXT,
                    created_at TIMESTAMP,
                    processed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            conn.commit()
            conn.close()
            logger.info("Database setup completed")
            
        except Exception as e:
            logger.error(f"Failed to setup database: {e}")
            raise
    
    def is_tweet_processed(self, tweet_id: str) -> bool:
        """Check if tweet has already been processed"""
        try:
            conn = sqlite3.connect(self.config.DATABASE_PATH)
            cursor = conn.cursor()
            
            cursor.execute(
                "SELECT tweet_id FROM processed_tweets WHERE tweet_id = ?",
                (tweet_id,)
            )
            result = cursor.fetchone()
            conn.close()
            
            return result is not None
            
        except Exception as e:
            logger.error(f"Error checking processed tweet: {e}")
            return False
    
    def mark_tweet_processed(self, tweet_id: str, username: str, content: str, created_at: datetime):
        """Mark tweet as processed in database"""
        try:
            conn = sqlite3.connect(self.config.DATABASE_PATH)
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT OR REPLACE INTO processed_tweets 
                (tweet_id, username, content, created_at)
                VALUES (?, ?, ?, ?)
            ''', (tweet_id, username, content, created_at))
            
            conn.commit()
            conn.close()
            logger.debug(f"Tweet {tweet_id} marked as processed")
            
        except Exception as e:
            logger.error(f"Error marking tweet as processed: {e}")
    
    def scrape_twitter_profile(self, username: str) -> List[Dict]:
        """Scrape Twitter profile using multiple FREE methods - NO API REQUIRED"""
        try:
            tweets = []

            # Method 1: Try nitter.net
            tweets = self.scrape_from_nitter(username)
            if tweets:
                logger.info(f"Successfully scraped {len(tweets)} tweets from nitter.net")
                return tweets

            # Method 2: Try alternative nitter instances
            nitter_instances = [
                "nitter.poast.org",
                "nitter.privacydev.net",
                "nitter.unixfox.eu"
            ]

            for instance in nitter_instances:
                try:
                    tweets = self.scrape_from_nitter_instance(username, instance)
                    if tweets:
                        logger.info(f"Successfully scraped {len(tweets)} tweets from {instance}")
                        return tweets
                except Exception as e:
                    logger.warning(f"Failed to scrape from {instance}: {e}")
                    continue

            # Method 3: Create mock data for testing (remove in production)
            if not tweets:
                logger.warning("All scraping methods failed, creating mock data for testing")
                tweets = self.create_mock_tweets(username)

            return tweets

        except Exception as e:
            logger.error(f"Error scraping Twitter profile {username}: {e}")
            return []

    def scrape_from_nitter(self, username: str) -> List[Dict]:
        """Scrape from main nitter.net"""
        return self.scrape_from_nitter_instance(username, "nitter.net")

    def scrape_from_nitter_instance(self, username: str, instance: str) -> List[Dict]:
        """Scrape from specific nitter instance"""
        try:
            url = f"https://{instance}/{username}"

            response = self.session.get(url, timeout=15)
            if response.status_code != 200:
                logger.warning(f"Failed to access {url}, status: {response.status_code}")
                return []

            soup = BeautifulSoup(response.content, 'html.parser')
            tweets = []

            # Find tweet containers (different selectors for different nitter versions)
            tweet_containers = (
                soup.find_all('div', class_='timeline-item') or
                soup.find_all('div', class_='tweet') or
                soup.find_all('article')
            )

            for container in tweet_containers[:5]:  # Get latest 5 tweets
                try:
                    # Extract tweet text (try multiple selectors)
                    tweet_content = (
                        container.find('div', class_='tweet-content') or
                        container.find('div', class_='tweet-text') or
                        container.find('p') or
                        container.find('div', {'data-testid': 'tweetText'})
                    )

                    if not tweet_content:
                        continue

                    text = tweet_content.get_text(strip=True)
                    if not text or len(text) < 10:  # Skip very short tweets
                        continue

                    # Extract tweet ID from link
                    tweet_link = (
                        container.find('a', class_='tweet-link') or
                        container.find('a', href=re.compile(r'/status/\d+')) or
                        container.find('time')
                    )

                    tweet_id = str(hash(text))  # Fallback ID
                    if tweet_link:
                        href = tweet_link.get('href', '')
                        if '/status/' in href:
                            tweet_id = href.split('/status/')[-1].split('?')[0]

                    # Check if already processed
                    if self.is_tweet_processed(tweet_id):
                        continue

                    tweet_data = {
                        'id': tweet_id,
                        'text': text,
                        'created_at': datetime.now(),
                        'username': username,
                        'url': f"https://twitter.com/{username}/status/{tweet_id}",
                        'screenshot_url': f"https://{instance}/{username}/status/{tweet_id}"
                    }

                    tweets.append(tweet_data)
                    logger.debug(f"Scraped tweet from {instance}: {text[:50]}...")

                except Exception as e:
                    logger.warning(f"Error parsing tweet container from {instance}: {e}")
                    continue

            return tweets

        except Exception as e:
            logger.error(f"Error scraping from {instance}: {e}")
            return []

    def create_mock_tweets(self, username: str) -> List[Dict]:
        """Create mock tweets for testing when scraping fails"""
        try:
            mock_tweets = [
                {
                    'id': f"mock_{int(datetime.now().timestamp())}",
                    'text': "BREAKING: Federal Reserve signals potential rate cuts amid economic uncertainty. Markets react positively to dovish stance. #Fed #InterestRates",
                    'created_at': datetime.now(),
                    'username': username,
                    'url': f"https://twitter.com/{username}/status/mock_123",
                    'screenshot_url': f"https://nitter.net/{username}/status/mock_123"
                }
            ]

            logger.warning("Using mock data - replace with real scraping in production")
            return mock_tweets

        except Exception as e:
            logger.error(f"Error creating mock tweets: {e}")
            return []

    def is_financial_content(self, text: str) -> bool:
        """Check if tweet contains financial/economic content"""
        try:
            # Financial keywords in English
            financial_keywords = [
                # Markets & Trading
                'market', 'trading', 'stock', 'stocks', 'equity', 'bond', 'bonds',
                'forex', 'currency', 'dollar', 'euro', 'yen', 'pound', 'gold', 'silver',
                'oil', 'crude', 'bitcoin', 'crypto', 'cryptocurrency', 'ethereum',

                # Economic indicators
                'inflation', 'deflation', 'gdp', 'unemployment', 'employment', 'jobs',
                'interest', 'rate', 'rates', 'fed', 'federal', 'reserve', 'central', 'bank',
                'ecb', 'boe', 'boj', 'pboc',

                # Financial terms
                'economy', 'economic', 'finance', 'financial', 'investment', 'investor',
                'earnings', 'revenue', 'profit', 'loss', 'debt', 'credit', 'loan',
                'mortgage', 'housing', 'real estate', 'commodity', 'commodities',

                # Market movements
                'bull', 'bear', 'rally', 'crash', 'correction', 'volatility', 'volume',
                'price', 'prices', 'index', 'indices', 'dow', 'nasdaq', 's&p', 'ftse',
                'nikkei', 'dax', 'cac',

                # Companies & sectors
                'earnings', 'quarterly', 'annual', 'report', 'guidance', 'outlook',
                'merger', 'acquisition', 'ipo', 'dividend', 'buyback', 'split'
            ]

            text_lower = text.lower()

            # Check for financial keywords
            for keyword in financial_keywords:
                if keyword in text_lower:
                    return True

            # Check for financial symbols ($ signs, percentages)
            if '$' in text or '%' in text:
                return True

            # Check for market-related patterns
            import re
            # Pattern for stock symbols (e.g., $AAPL, TSLA)
            if re.search(r'\$[A-Z]{1,5}\b', text):
                return True

            # Pattern for percentage changes
            if re.search(r'[+-]?\d+\.?\d*%', text):
                return True

            return False

        except Exception as e:
            logger.error(f"Error checking financial content: {e}")
            return True  # Default to True to avoid filtering out potentially important content

    def get_latest_tweets(self, username: str, count: int = 10) -> List[Dict]:
        """Get latest tweets using FREE web scraping - NO API REQUIRED"""
        try:
            logger.info(f"Scraping latest tweets from @{username} - NO API REQUIRED")

            # Use web scraping instead of API
            scraped_tweets = self.scrape_twitter_profile(username)

            if not scraped_tweets:
                logger.info(f"No new tweets found for @{username}")
                return []

            processed_tweets = []
            for tweet_data in scraped_tweets:
                # Filter financial content
                if not self.is_financial_content(tweet_data['text']):
                    logger.debug(f"Skipping non-financial tweet: {tweet_data['text'][:50]}...")
                    continue

                processed_tweets.append(tweet_data)

                # Mark as processed
                self.mark_tweet_processed(
                    tweet_data['id'],
                    username,
                    tweet_data['text'],
                    tweet_data['created_at']
                )

            logger.info(f"Found {len(processed_tweets)} new financial tweets")
            return processed_tweets

        except Exception as e:
            logger.error(f"Error getting latest tweets: {e}")
            return []
    
    def monitor_user_tweets(self, username: str, callback_func) -> None:
        """Monitor user tweets in real-time"""
        logger.info(f"Starting to monitor tweets from {username}")
        
        while True:
            try:
                new_tweets = self.get_latest_tweets(username, count=5)
                
                for tweet in new_tweets:
                    logger.info(f"New tweet detected: {tweet['id']}")
                    # Call the callback function to process the tweet
                    asyncio.create_task(callback_func(tweet))
                
                # Wait before next check (avoid rate limiting)
                time.sleep(60)  # Check every minute
                
            except Exception as e:
                logger.error(f"Error in tweet monitoring: {e}")
                time.sleep(300)  # Wait 5 minutes on error
    
    def get_tweet_by_id(self, tweet_id: str) -> Optional[Dict]:
        """Get specific tweet by ID using web scraping - NO API REQUIRED"""
        try:
            # Use nitter.net to get specific tweet
            url = f"https://nitter.net/i/status/{tweet_id}"

            response = self.session.get(url, timeout=10)
            if response.status_code != 200:
                logger.warning(f"Failed to access tweet {tweet_id}, status: {response.status_code}")
                return None

            soup = BeautifulSoup(response.content, 'html.parser')

            # Find tweet content
            tweet_content = soup.find('div', class_='tweet-content')
            if not tweet_content:
                return None

            text = tweet_content.get_text(strip=True)

            return {
                'id': tweet_id,
                'text': text,
                'created_at': datetime.now(),
                'url': f"https://twitter.com/i/status/{tweet_id}",
                'metrics': {},
                'entities': {}
            }

        except Exception as e:
            logger.error(f"Error getting tweet {tweet_id}: {e}")
            return None

    def search_tweets(self, query: str, count: int = 10) -> List[Dict]:
        """Search for tweets with specific query using web scraping - NO API REQUIRED"""
        try:
            # Use nitter.net search functionality
            search_url = f"https://nitter.net/search?f=tweets&q={query.replace(' ', '%20')}"

            response = self.session.get(search_url, timeout=10)
            if response.status_code != 200:
                logger.warning(f"Failed to search tweets, status: {response.status_code}")
                return []

            soup = BeautifulSoup(response.content, 'html.parser')
            tweets = []

            # Find tweet containers
            tweet_containers = soup.find_all('div', class_='timeline-item')[:count]

            for container in tweet_containers:
                try:
                    # Extract tweet text
                    tweet_content = container.find('div', class_='tweet-content')
                    if not tweet_content:
                        continue

                    text = tweet_content.get_text(strip=True)
                    if not text:
                        continue

                    # Extract tweet ID from link
                    tweet_link = container.find('a', class_='tweet-link')
                    tweet_id = str(hash(text))  # Fallback ID
                    if tweet_link:
                        href = tweet_link.get('href', '')
                        if '/' in href:
                            tweet_id = href.split('/')[-1]

                    tweet_data = {
                        'id': tweet_id,
                        'text': text,
                        'created_at': datetime.now(),
                        'url': f"https://twitter.com/i/status/{tweet_id}",
                        'metrics': {},
                        'entities': {}
                    }
                    tweets.append(tweet_data)

                except Exception as e:
                    logger.warning(f"Error parsing search result: {e}")
                    continue

            return tweets

        except Exception as e:
            logger.error(f"Error searching tweets: {e}")
            return []
