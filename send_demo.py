"""
ارسال پیام تست برای نمایش به کارفرما
"""
import asyncio
from telegram import Bot

async def send_demo_post():
    """Send demo post to show employer the final format"""
    try:
        bot = Bot('8207363120:AAHL0OJoGCgehj3eQEF1Kh6HJmREKhe-vvw')
        channel_id = '@FinancialJuiceFarsi'
        
        # Demo post with exact format requested by employer
        demo_post = """📝 *ترجمه فارسی توییت:*
بانک مرکزی آمریکا (فدرال رزرو) نرخ بهره را ۰.۲۵ درصد افزایش داد تا با تورم مبارزه کند. این تصمیم در نشست FOMC اتخاذ شد.

📌 *پ.ن:*
*Fed:* بانک مرکزی آمریکا - تصمیم‌گیرنده اصلی سیاست پولی
*FOMC:* کمیته بازار باز فدرال - نهاد تعیین‌کننده نرخ بهره

📊 *تحلیل سریع برای تریدرها:*
تحلیل مختصر بر اساس اقتصاد کلان و خرد:
• *اقتصاد کلان:* تأثیر بر سیاست پولی آمریکا، کنترل تورم
• *بازارهای مالی:* تقویت احتمالی دلار، فشار بر طلا و سهام

📢 @FinancialJuiceFarsi

---
🤖 *این یک پیام تست برای نمایش قالب نهایی است*"""
        
        print("📤 Sending demo post for employer...")
        message = await bot.send_message(
            chat_id=channel_id,
            text=demo_post,
            parse_mode='Markdown'
        )
        
        print(f"✅ Demo post sent successfully!")
        print(f"📱 Link: https://t.me/FinancialJuiceFarsi/{message.message_id}")
        print(f"🆔 Message ID: {message.message_id}")
        
        return message.message_id
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return None

if __name__ == "__main__":
    print("🎭 DEMO POST - For Employer Review")
    print("=" * 50)
    
    message_id = asyncio.run(send_demo_post())
    
    if message_id:
        print(f"\n🎉 Demo sent! Message ID: {message_id}")
        print("📋 Tell employer to check the channel now!")
    else:
        print("\n❌ Failed to send demo")
