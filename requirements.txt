# Core dependencies - NO PAID APIs REQUIRED
python-telegram-bot==20.7
requests>=2.31.0
python-dotenv>=1.0.0

# Web scraping (instead of Twitter API)
beautifulsoup4>=4.12.0
lxml>=4.9.0

# Image processing for screenshots
Pillow>=10.0.0

# Translation services (FREE)
deep-translator>=1.11.0
# googletrans==4.0.0rc1  # Commented out due to httpcore compatibility issues

# Data processing
pandas>=2.0.0
numpy>=1.24.0

# Async and scheduling
aiohttp>=3.9.0
httpcore>=0.18.0

# Logging and monitoring
loguru>=0.7.0

# Configuration and utilities
python-dateutil>=2.8.0

# HTTP client compatibility
httpx>=0.25.0

# Optional: Twitter API (if available)
# tweepy>=4.14.0

# Optional: OpenAI (NOT REQUIRED - FREE ALTERNATIVES USED)
# openai>=1.3.0
