"""
Simple Translation Engine - No external dependencies
"""
import json
import re
from typing import Dict, Optional

class SimpleTranslationEngine:
    """Simple translation engine with basic functionality"""
    
    def __init__(self, config):
        self.config = config
        self.financial_terms = {}
        self.load_financial_terms()
        
        # Basic translation dictionary
        self.basic_translations = {
            'Federal Reserve': 'فدرال رزرو',
            'Fed': 'فدرال رزرو',
            'interest rate': 'نرخ بهره',
            'interest rates': 'نرخ‌های بهره',
            'inflation': 'تورم',
            'FOMC': 'کمیته بازار باز فدرال',
            'hawkish': 'سخت‌گیرانه',
            'dovish': 'نرم',
            'GDP': 'تولید ناخالص داخلی',
            'CPI': 'شاخص قیمت مصرف‌کننده',
            'NFP': 'اشتغال غیرکشاورزی',
            'Powell': 'پاول',
            'Chair': 'رئیس',
            'meeting': 'جلسه',
            'signals': 'اعلام می‌کند',
            'raises': 'افزایش می‌دهد',
            'cuts': 'کاهش می‌دهد',
            'outlook': 'چشم‌انداز',
            'economy': 'اقتصاد',
            'economic': 'اقتصادی',
            'growth': 'رشد',
            'recession': 'رکود',
            'market': 'بازار',
            'markets': 'بازارها',
            'stock': 'سهام',
            'stocks': 'سهام',
            'dollar': 'دلار',
            'gold': 'طلا',
            'oil': 'نفت',
            'bitcoin': 'بیت کوین',
            'crypto': 'ارز دیجیتال',
            'cryptocurrency': 'ارز دیجیتال',
            'trading': 'معاملات',
            'traders': 'تریدرها',
            'investors': 'سرمایه‌گذاران',
            'investment': 'سرمایه‌گذاری',
            'earnings': 'درآمد',
            'revenue': 'درآمد',
            'profit': 'سود',
            'loss': 'زیان',
            'bullish': 'صعودی',
            'bearish': 'نزولی',
            'volatility': 'نوسان',
            'support': 'حمایت',
            'resistance': 'مقاومت',
            'breakout': 'شکست',
            'correction': 'اصلاح',
            'rally': 'رشد',
            'decline': 'کاهش',
            'surge': 'جهش',
            'plunge': 'سقوط',
            'BREAKING': 'فوری',
            'UPDATE': 'به‌روزرسانی',
            'ALERT': 'هشدار'
        }
    
    def load_financial_terms(self):
        """Load financial terms from file"""
        try:
            with open(self.config.FINANCIAL_TERMS_FILE, 'r', encoding='utf-8') as f:
                data = json.load(f)
                self.financial_terms = data.get('financial_terms', {})
            print(f"Loaded {len(self.financial_terms)} financial terms")
        except Exception as e:
            print(f"Could not load financial terms: {e}")
            self.financial_terms = {}
    
    def translate_text(self, text: str) -> str:
        """Translate text using simple word replacement"""
        try:
            print(f"Translating: {text[:50]}...")
            
            result = text
            
            # Replace financial terms from file
            for english, persian in self.financial_terms.items():
                result = self._replace_word(result, english, persian)
            
            # Replace basic translations
            for english, persian in self.basic_translations.items():
                result = self._replace_word(result, english, persian)
            
            # Convert numbers to Persian
            result = self.convert_numbers_to_persian(result)
            
            # Basic sentence structure fixes
            result = self.fix_sentence_structure(result)
            
            print(f"Translation result: {result[:50]}...")
            return result
            
        except Exception as e:
            print(f"Translation error: {e}")
            return text
    
    def _replace_word(self, text: str, english: str, persian: str) -> str:
        """Replace word with case-insensitive matching"""
        # Exact match
        text = text.replace(english, persian)
        # Lowercase match
        text = text.replace(english.lower(), persian)
        # Uppercase match
        text = text.replace(english.upper(), persian)
        # Title case match
        text = text.replace(english.title(), persian)
        return text
    
    def convert_numbers_to_persian(self, text: str) -> str:
        """Convert English numbers to Persian"""
        english_to_persian = {
            '0': '۰', '1': '۱', '2': '۲', '3': '۳', '4': '۴',
            '5': '۵', '6': '۶', '7': '۷', '8': '۸', '9': '۹'
        }
        
        result = text
        for eng, per in english_to_persian.items():
            result = result.replace(eng, per)
        
        return result
    
    def fix_sentence_structure(self, text: str) -> str:
        """Basic sentence structure improvements"""
        # Remove extra spaces
        text = re.sub(r'\s+', ' ', text).strip()
        
        # Fix common patterns
        replacements = {
            'درصد': '%',
            'دلار آمریکا': 'دلار',
            'ایالات متحده آمریکا': 'آمریکا',
            'بازار سهام': 'بازار',
            ' ، ': '، ',
            ' . ': '. '
        }
        
        for old, new in replacements.items():
            text = text.replace(old, new)
        
        return text
    
    def get_translation_confidence(self, original: str, translated: str) -> float:
        """Simple confidence calculation"""
        if not translated or translated == original:
            return 0.0
        
        # Count how many words were translated
        original_words = original.lower().split()
        translated_different = translated != original
        
        if translated_different:
            return 0.8  # Good confidence for basic translation
        else:
            return 0.2  # Low confidence if nothing changed
