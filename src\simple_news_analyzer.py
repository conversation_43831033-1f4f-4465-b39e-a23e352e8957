"""
Simple News Analyzer - No external dependencies
"""
import re
from typing import Dict

class SimpleNewsAnalyzer:
    """Simple news analyzer for financial content"""
    
    def __init__(self, config):
        self.config = config
        
        # Financial keywords for filtering
        self.financial_keywords = [
            'fed', 'federal reserve', 'interest rate', 'inflation', 'gdp', 'unemployment',
            'stock', 'market', 'trading', 'forex', 'currency', 'dollar', 'euro',
            'gold', 'oil', 'bitcoin', 'crypto', 'economy', 'economic', 'financial',
            'bank', 'central bank', 'ecb', 'boe', 'fomc', 'nfp', 'cpi', 'ppi',
            'powell', 'yellen', 'lagarde', 'earnings', 'revenue', 'profit'
        ]
        
        # Market impact keywords
        self.bullish_keywords = [
            'raise', 'increase', 'growth', 'surge', 'rally', 'bullish', 'positive',
            'strong', 'beat', 'exceed', 'outperform', 'gain', 'rise', 'up'
        ]
        
        self.bearish_keywords = [
            'cut', 'decrease', 'decline', 'fall', 'crash', 'bearish', 'negative',
            'weak', 'miss', 'underperform', 'loss', 'drop', 'down', 'plunge'
        ]
    
    def is_financial_news(self, text: str) -> bool:
        """Check if text is financial/economic news"""
        text_lower = text.lower()
        
        # Check for financial keywords
        financial_score = 0
        for keyword in self.financial_keywords:
            if keyword in text_lower:
                financial_score += 1
        
        # Must have at least 1 financial keyword
        return financial_score >= 1
    
    def analyze_news(self, original_text: str, translated_text: str) -> Dict:
        """Analyze news and create trading analysis"""
        try:
            print("Analyzing news...")
            
            # Check if it's financial news
            if not self.is_financial_news(original_text):
                return {
                    'analysis': "",
                    'sentiment': 'filtered',
                    'confidence': 0.0,
                    'key_info': {},
                    'analysis_method': 'filtered_out'
                }
            
            # Determine sentiment
            sentiment, confidence = self.determine_sentiment(original_text)
            
            # Create trading analysis
            analysis = self.create_trading_analysis(original_text, sentiment)
            
            result = {
                'analysis': analysis,
                'sentiment': sentiment,
                'confidence': confidence,
                'key_info': self.extract_key_info(original_text),
                'analysis_method': 'simple_analysis'
            }
            
            print(f"Analysis completed: {sentiment} sentiment")
            return result
            
        except Exception as e:
            print(f"Analysis error: {e}")
            return {
                'analysis': "",
                'sentiment': 'error',
                'confidence': 0.0,
                'key_info': {},
                'analysis_method': 'error'
            }
    
    def determine_sentiment(self, text: str) -> tuple:
        """Determine sentiment of the news"""
        text_lower = text.lower()
        
        bullish_count = sum(1 for word in self.bullish_keywords if word in text_lower)
        bearish_count = sum(1 for word in self.bearish_keywords if word in text_lower)
        
        if bullish_count > bearish_count:
            sentiment = 'positive'
            confidence = min(0.8, 0.5 + (bullish_count - bearish_count) * 0.1)
        elif bearish_count > bullish_count:
            sentiment = 'negative'
            confidence = min(0.8, 0.5 + (bearish_count - bullish_count) * 0.1)
        else:
            sentiment = 'neutral'
            confidence = 0.5
        
        return sentiment, confidence
    
    def extract_key_info(self, text: str) -> Dict:
        """Extract key information from text"""
        info = {
            'financial_terms': [],
            'market_symbols': [],
            'numbers_and_percentages': []
        }
        
        # Extract percentages
        percentages = re.findall(r'\d+\.?\d*\s*%', text)
        info['numbers_and_percentages'] = percentages
        
        # Extract market symbols
        symbols = re.findall(r'\$[A-Z]{1,5}', text)
        info['market_symbols'] = symbols
        
        # Extract financial terms
        text_lower = text.lower()
        for keyword in self.financial_keywords:
            if keyword in text_lower:
                info['financial_terms'].append(keyword)
        
        return info
    
    def create_trading_analysis(self, text: str, sentiment: str) -> str:
        """Create professional trading analysis"""
        try:
            text_lower = text.lower()
            analysis_parts = []
            
            # Start with header
            analysis_parts.append("📊 *تحلیل سریع برای تریدرها:*")
            
            # Forex analysis
            forex_analysis = self.analyze_forex_impact(text_lower, sentiment)
            if forex_analysis:
                analysis_parts.extend(forex_analysis)
            
            # Gold analysis
            gold_analysis = self.analyze_gold_impact(text_lower, sentiment)
            if gold_analysis:
                analysis_parts.extend(gold_analysis)
            
            # Stock indices analysis
            stock_analysis = self.analyze_stock_impact(text_lower, sentiment)
            if stock_analysis:
                analysis_parts.extend(stock_analysis)
            
            # Crypto analysis
            crypto_analysis = self.analyze_crypto_impact(text_lower, sentiment)
            if crypto_analysis:
                analysis_parts.extend(crypto_analysis)
            
            # Economic context
            context = self.get_economic_context(text_lower)
            if context:
                analysis_parts.append(f"\n*زمینه اقتصادی:* {context}")
            
            return "\n".join(analysis_parts)
            
        except Exception as e:
            print(f"Error creating trading analysis: {e}")
            return "📊 *تحلیل سریع برای تریدرها:*\n• این خبر برای بازارهای مالی قابل توجه است"
    
    def analyze_forex_impact(self, text_lower: str, sentiment: str) -> list:
        """Analyze forex market impact"""
        impacts = []
        
        if any(word in text_lower for word in ['fed', 'fomc', 'powell', 'interest rate']):
            if sentiment == 'positive':
                impacts.append("• *فارکس (EUR/USD):* فشار نزولی - تقویت احتمالی دلار")
                impacts.append("• *فارکس (GBP/USD):* احتمال کاهش - دلار قوی‌تر")
            elif sentiment == 'negative':
                impacts.append("• *فارکس (EUR/USD):* احتمال رشد - تضعیف دلار")
                impacts.append("• *فارکس (GBP/USD):* فرصت صعود - دلار ضعیف‌تر")
        
        return impacts
    
    def analyze_gold_impact(self, text_lower: str, sentiment: str) -> list:
        """Analyze gold market impact"""
        impacts = []
        
        if any(word in text_lower for word in ['inflation', 'uncertainty', 'crisis', 'gold']):
            if sentiment == 'negative':
                impacts.append("• *طلا (XAU/USD):* احتمال رشد - تقاضای امن‌پناهی")
            elif sentiment == 'positive' and 'rate' in text_lower:
                impacts.append("• *طلا (XAU/USD):* فشار نزولی - رقابت با دلار قوی")
        
        return impacts
    
    def analyze_stock_impact(self, text_lower: str, sentiment: str) -> list:
        """Analyze stock market impact"""
        impacts = []
        
        if any(word in text_lower for word in ['earnings', 'stock', 'market', 'economy']):
            if sentiment == 'positive':
                impacts.append("• *شاخص‌ها (S&P 500):* احتمال رشد - بهبود چشم‌انداز")
            elif sentiment == 'negative':
                impacts.append("• *شاخص‌ها (S&P 500):* ریسک نزول - نگرانی‌های اقتصادی")
        
        return impacts
    
    def analyze_crypto_impact(self, text_lower: str, sentiment: str) -> list:
        """Analyze crypto market impact"""
        impacts = []
        
        if any(word in text_lower for word in ['bitcoin', 'crypto', 'digital']):
            if sentiment == 'negative':
                impacts.append("• *ارزهای دیجیتال (BTC):* ریسک نزول - فشار نظارتی")
            elif sentiment == 'positive':
                impacts.append("• *ارزهای دیجیتال (BTC):* احتمال رشد - پذیرش نهادی")
        elif 'rate' in text_lower:
            impacts.append("• *ارزهای دیجیتال (BTC):* نوسان بالا - حساسیت به نرخ بهره")
        
        return impacts
    
    def get_economic_context(self, text_lower: str) -> str:
        """Get economic context for the news"""
        if any(word in text_lower for word in ['fed', 'fomc', 'powell']):
            return "تصمیمات فدرال رزرو بر تمام بازارهای جهانی تأثیرگذار است"
        elif any(word in text_lower for word in ['inflation', 'cpi']):
            return "تورم عامل کلیدی در تصمیمات بانک‌های مرکزی محسوب می‌شود"
        elif any(word in text_lower for word in ['gdp', 'growth', 'recession']):
            return "رشد اقتصادی مستقیماً بر ارزش دارایی‌های پرریسک تأثیر می‌گذارد"
        else:
            return ""
