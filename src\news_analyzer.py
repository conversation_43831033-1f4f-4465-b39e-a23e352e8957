"""
News Analyzer Module for Financial News Translation Bot
Provides intelligent analysis of financial news content
FREE VERSION - No paid APIs
"""
import re
import json
import requests
from typing import Dict, List, Optional, Tuple
from loguru import logger
from config import Config

class NewsAnalyzer:
    """Intelligent news analyzer for financial content"""
    
    def __init__(self, config: Config):
        self.config = config
        self.financial_terms = {}
        self.market_symbols = {}

        # Financial keywords for filtering
        self.financial_keywords = [
            'fed', 'federal reserve', 'interest rate', 'inflation', 'gdp', 'unemployment',
            'stock', 'market', 'trading', 'forex', 'currency', 'dollar', 'euro',
            'gold', 'oil', 'bitcoin', 'crypto', 'economy', 'economic', 'financial',
            'bank', 'central bank', 'ecb', 'boe', 'fomc', 'nfp', 'cpi', 'ppi'
        ]

        # Anti-Iran keywords to filter out
        self.anti_iran_keywords = [
            'iran sanction', 'iran nuclear', 'iran threat', 'iran regime',
            'iranian sanction', 'iranian nuclear', 'iranian threat'
        ]

        self.load_financial_terms()

    def is_financial_news(self, text: str) -> bool:
        """Check if text is financial/economic news"""
        try:
            text_lower = text.lower()

            # Check for anti-Iran content
            for keyword in self.anti_iran_keywords:
                if keyword in text_lower:
                    logger.info(f"Filtered out anti-Iran content: {keyword}")
                    return False

            # Check for financial keywords
            financial_score = 0
            for keyword in self.financial_keywords:
                if keyword in text_lower:
                    financial_score += 1

            # Must have at least 1 financial keyword
            is_financial = financial_score >= 1

            if is_financial:
                logger.info(f"Financial news detected (score: {financial_score})")
            else:
                logger.info("Non-financial content filtered out")

            return is_financial

        except Exception as e:
            logger.error(f"Error checking financial news: {e}")
            return False

    def create_professional_trading_analysis(self, original_text: str, translated_text: str) -> str:
        """Create professional trading analysis as requested by employer"""
        try:
            text_lower = original_text.lower()
            analysis_parts = []

            # Analyze specific market impacts based on employer requirements
            market_impacts = self.analyze_detailed_market_impacts(text_lower)

            if market_impacts:
                analysis_parts.append("📊 *تحلیل سریع برای تریدرها:*")

                # Add each market impact
                for market, impact in market_impacts.items():
                    if impact:
                        analysis_parts.append(f"• *{market}:* {impact}")

                # Add economic context if relevant
                economic_context = self.get_economic_context(text_lower)
                if economic_context:
                    analysis_parts.append(f"\n*زمینه اقتصادی:* {economic_context}")

            return "\n".join(analysis_parts) if analysis_parts else ""

        except Exception as e:
            logger.error(f"Error creating trading analysis: {e}")
            return ""

    def analyze_detailed_market_impacts(self, text_lower: str) -> Dict[str, str]:
        """Analyze detailed market impacts for different asset classes"""
        impacts = {}

        # Forex Analysis
        if any(word in text_lower for word in ['fed', 'fomc', 'powell', 'interest rate', 'monetary policy']):
            if any(word in text_lower for word in ['hawkish', 'raise', 'hike', 'tighten']):
                impacts['فارکس (EUR/USD)'] = 'فشار نزولی - تقویت احتمالی دلار'
                impacts['فارکس (GBP/USD)'] = 'احتمال کاهش - دلار قوی‌تر'
            elif any(word in text_lower for word in ['dovish', 'cut', 'lower', 'ease']):
                impacts['فارکس (EUR/USD)'] = 'احتمال رشد - تضعیف دلار'
                impacts['فارکس (GBP/USD)'] = 'فرصت صعود - دلار ضعیف‌تر'

        # Gold Analysis
        if any(word in text_lower for word in ['inflation', 'uncertainty', 'crisis', 'safe haven']):
            impacts['طلا (XAU/USD)'] = 'احتمال رشد - تقاضای امن‌پناهی'
        elif any(word in text_lower for word in ['rate hike', 'strong dollar', 'hawkish']):
            impacts['طلا (XAU/USD)'] = 'فشار نزولی - رقابت با دلار قوی'

        # Stock Indices
        if any(word in text_lower for word in ['earnings', 'profit', 'growth', 'bullish']):
            impacts['شاخص‌ها (S&P 500)'] = 'احتمال رشد - بهبود چشم‌انداز'
        elif any(word in text_lower for word in ['recession', 'decline', 'bearish', 'crash']):
            impacts['شاخص‌ها (S&P 500)'] = 'ریسک نزول - نگرانی‌های اقتصادی'

        # Crypto Analysis
        if any(word in text_lower for word in ['bitcoin', 'crypto', 'digital asset']):
            if any(word in text_lower for word in ['regulation', 'ban', 'restrict']):
                impacts['ارزهای دیجیتال (BTC)'] = 'ریسک نزول - فشار نظارتی'
            elif any(word in text_lower for word in ['adoption', 'institutional', 'etf']):
                impacts['ارزهای دیجیتال (BTC)'] = 'احتمال رشد - پذیرش نهادی'

        # Oil Analysis
        if any(word in text_lower for word in ['oil', 'crude', 'opec', 'energy']):
            if any(word in text_lower for word in ['production cut', 'supply shortage']):
                impacts['نفت (WTI/Brent)'] = 'احتمال رشد - کاهش عرضه'
            elif any(word in text_lower for word in ['oversupply', 'demand decline']):
                impacts['نفت (WTI/Brent)'] = 'فشار نزولی - اضافه عرضه'

        return impacts

    def get_economic_context(self, text_lower: str) -> str:
        """Get economic context for the news"""
        contexts = []

        if any(word in text_lower for word in ['fed', 'fomc', 'monetary policy']):
            contexts.append("تصمیمات فدرال رزرو بر تمام بازارهای جهانی تأثیرگذار است")

        if any(word in text_lower for word in ['inflation', 'cpi']):
            contexts.append("تورم عامل کلیدی در تصمیمات بانک‌های مرکزی محسوب می‌شود")

        if any(word in text_lower for word in ['gdp', 'growth', 'recession']):
            contexts.append("رشد اقتصادی مستقیماً بر ارزش دارایی‌های پرریسک تأثیر می‌گذارد")

        return contexts[0] if contexts else ""

    def load_financial_terms(self):
        """Load financial terms for analysis"""
        try:
            with open(self.config.FINANCIAL_TERMS_FILE, 'r', encoding='utf-8') as f:
                data = json.load(f)
                self.financial_terms = data.get('financial_terms', {})
                self.market_symbols = data.get('market_symbols', {})
            
            logger.info("Financial terms loaded for analysis")
            
        except Exception as e:
            logger.error(f"Error loading financial terms for analysis: {e}")
            self.financial_terms = {}
            self.market_symbols = {}
    
    def extract_key_information(self, text: str) -> Dict[str, any]:
        """Extract key financial information from text"""
        info = {
            'market_symbols': [],
            'financial_terms': [],
            'numbers_and_percentages': [],
            'sentiment_indicators': [],
            'time_indicators': [],
            'companies': []
        }
        
        try:
            # Extract market symbols
            symbol_pattern = r'\$[A-Z]{1,5}'
            info['market_symbols'] = re.findall(symbol_pattern, text)
            
            # Extract percentages and numbers
            number_pattern = r'[\d,]+\.?\d*\s*%|[\d,]+\.?\d*\s*\$|[\d,]+\.?\d*\s*€|[\d,]+\.?\d*\s*billion|[\d,]+\.?\d*\s*million'
            info['numbers_and_percentages'] = re.findall(number_pattern, text)
            
            # Extract financial terms
            for term in self.financial_terms.keys():
                if term.lower() in text.lower():
                    info['financial_terms'].append(term)
            
            # Extract sentiment indicators
            positive_words = ['up', 'rise', 'gain', 'growth', 'increase', 'bull', 'rally', 'surge', 'boost']
            negative_words = ['down', 'fall', 'loss', 'decline', 'decrease', 'bear', 'crash', 'drop', 'plunge']
            
            text_lower = text.lower()
            for word in positive_words:
                if word in text_lower:
                    info['sentiment_indicators'].append(('positive', word))
            
            for word in negative_words:
                if word in text_lower:
                    info['sentiment_indicators'].append(('negative', word))
            
            # Extract time indicators
            time_patterns = [
                r'Q[1-4]', r'\d{4}', r'today', r'yesterday', r'this week', r'next week',
                r'this month', r'next month', r'this year', r'next year'
            ]
            
            for pattern in time_patterns:
                matches = re.findall(pattern, text, re.IGNORECASE)
                info['time_indicators'].extend(matches)
            
            return info
            
        except Exception as e:
            logger.error(f"Error extracting key information: {e}")
            return info
    
    def determine_sentiment(self, text: str, key_info: Dict) -> Tuple[str, float]:
        """Determine overall sentiment of the news"""
        try:
            positive_count = len([s for s in key_info['sentiment_indicators'] if s[0] == 'positive'])
            negative_count = len([s for s in key_info['sentiment_indicators'] if s[0] == 'negative'])
            
            # Check for percentage changes
            percentages = []
            for item in key_info['numbers_and_percentages']:
                if '%' in item:
                    # Extract the number
                    num_match = re.search(r'([-+]?)(\d+\.?\d*)', item)
                    if num_match:
                        sign = num_match.group(1)
                        value = float(num_match.group(2))
                        if sign == '-':
                            value = -value
                        percentages.append(value)
            
            # Calculate sentiment score
            sentiment_score = 0.0
            
            # Word-based sentiment
            if positive_count > negative_count:
                sentiment_score += 0.3
            elif negative_count > positive_count:
                sentiment_score -= 0.3
            
            # Percentage-based sentiment
            if percentages:
                avg_percentage = sum(percentages) / len(percentages)
                if avg_percentage > 0:
                    sentiment_score += min(avg_percentage / 10, 0.5)
                else:
                    sentiment_score += max(avg_percentage / 10, -0.5)
            
            # Determine sentiment category
            if sentiment_score > 0.2:
                sentiment = 'positive'
            elif sentiment_score < -0.2:
                sentiment = 'negative'
            else:
                sentiment = 'neutral'
            
            confidence = min(abs(sentiment_score), 1.0)
            
            return sentiment, confidence
            
        except Exception as e:
            logger.error(f"Error determining sentiment: {e}")
            return 'neutral', 0.5
    
    def generate_analysis_with_ai(self, text: str, translated_text: str) -> Optional[str]:
        """Generate analysis using OpenAI"""
        try:
            if not self.config.OPENAI_API_KEY:
                return None
            
            prompt = f"""
            You are a financial news analyst. Analyze the following financial news and provide a brief analysis in Persian (Farsi).
            
            Original English text: "{text}"
            Persian translation: "{translated_text}"
            
            Please provide a concise analysis (maximum 2-3 sentences) in Persian that includes:
            1. The main impact or significance of this news
            2. What it means for investors or the market
            3. Any important context or implications
            
            Keep the analysis professional, informative, and easy to understand.
            
            Analysis in Persian:
            """
            
            response = openai.ChatCompletion.create(
                model="gpt-3.5-turbo",
                messages=[
                    {"role": "system", "content": "You are a professional financial analyst who provides insights in Persian."},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=200,
                temperature=0.4
            )
            
            analysis = response.choices[0].message.content.strip()
            logger.debug(f"AI-generated analysis: {analysis}")
            return analysis
            
        except Exception as e:
            logger.error(f"AI analysis error: {e}")
            return None
    
    def generate_rule_based_analysis(self, text: str, key_info: Dict, sentiment: str) -> str:
        """Generate analysis using rule-based approach"""
        try:
            analysis_parts = []
            
            # Market symbols analysis
            if key_info['market_symbols']:
                symbols = ', '.join([self.market_symbols.get(s, s) for s in key_info['market_symbols']])
                if sentiment == 'positive':
                    analysis_parts.append(f"این خبر می‌تواند تأثیر مثبتی بر {symbols} داشته باشد.")
                elif sentiment == 'negative':
                    analysis_parts.append(f"این خبر ممکن است فشار نزولی بر {symbols} وارد کند.")
                else:
                    analysis_parts.append(f"این خبر مربوط به {symbols} است.")
            
            # Percentage analysis
            percentages = [item for item in key_info['numbers_and_percentages'] if '%' in item]
            if percentages:
                if sentiment == 'positive':
                    analysis_parts.append("رشد قابل توجهی در شاخص‌های مالی مشاهده می‌شود.")
                elif sentiment == 'negative':
                    analysis_parts.append("کاهش در شاخص‌های کلیدی نگران‌کننده است.")
            
            # Financial terms analysis
            important_terms = ['earnings', 'revenue', 'profit', 'loss', 'GDP', 'inflation']
            found_important_terms = [term for term in key_info['financial_terms'] if term.lower() in important_terms]
            
            if found_important_terms:
                if 'earnings' in [t.lower() for t in found_important_terms]:
                    analysis_parts.append("گزارش درآمد شرکت می‌تواند بر قیمت سهام تأثیرگذار باشد.")
                elif 'gdp' in [t.lower() for t in found_important_terms]:
                    analysis_parts.append("تغییرات در GDP نشان‌دهنده وضعیت کلی اقتصاد است.")
                elif 'inflation' in [t.lower() for t in found_important_terms]:
                    analysis_parts.append("نرخ تورم بر تصمیمات بانک مرکزی تأثیر می‌گذارد.")
            
            # Default analysis if no specific patterns found
            if not analysis_parts:
                if sentiment == 'positive':
                    analysis_parts.append("این خبر احتمالاً تأثیر مثبتی بر بازارهای مالی خواهد داشت.")
                elif sentiment == 'negative':
                    analysis_parts.append("این خبر ممکن است نگرانی‌هایی در بازار ایجاد کند.")
                else:
                    analysis_parts.append("این خبر برای درک بهتر وضعیت بازار مفید است.")
            
            return ' '.join(analysis_parts[:2])  # Limit to 2 sentences
            
        except Exception as e:
            logger.error(f"Error generating rule-based analysis: {e}")
            return "این خبر برای سرمایه‌گذاران قابل توجه است."
    
    def analyze_news(self, original_text: str, translated_text: str) -> Dict[str, any]:
        """Main analysis function with employer's requirements"""
        try:
            logger.info("Starting news analysis...")

            # STEP 1: Check if it's financial news (filter)
            if not self.is_financial_news(original_text):
                return {
                    'analysis': "",
                    'sentiment': 'filtered',
                    'confidence': 0.0,
                    'key_info': {},
                    'analysis_method': 'filtered_out'
                }

            # STEP 2: Extract key information
            key_info = self.extract_key_information(original_text)

            # STEP 3: Determine sentiment
            sentiment, confidence = self.determine_sentiment(original_text, key_info)

            # STEP 4: Generate professional trading analysis as requested
            analysis = self.create_professional_trading_analysis(original_text, translated_text)

            # STEP 5: Fallback if no analysis generated
            if not analysis:
                analysis = self.generate_rule_based_analysis(original_text, key_info, sentiment)

            # Ensure analysis is not too long
            if len(analysis) > 300:  # Shorter for better readability
                analysis = analysis[:297] + "..."

            result = {
                'analysis': analysis,
                'sentiment': sentiment,
                'confidence': confidence,
                'key_info': key_info,
                'analysis_method': 'macro_micro'
            }

            logger.info(f"Analysis completed: {analysis[:50]}...")
            return result

        except Exception as e:
            logger.error(f"Error in news analysis: {e}")
            return {
                'analysis': "",
                'sentiment': 'error',
                'confidence': 0.0,
                'key_info': {},
                'analysis_method': 'error'
            }
