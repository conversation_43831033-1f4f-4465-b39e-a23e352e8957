"""
Test the fixed bot components
"""
import sys
import os
import asyncio

# Add src directory to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

def test_config():
    """Test config loading"""
    print("🔧 Testing Config...")
    try:
        from config import get_config
        config = get_config('production')
        print(f"✅ Config loaded")
        print(f"   Bot Token: {config.TELEGRAM_BOT_TOKEN[:20]}...")
        print(f"   Channel: {config.TELEGRAM_CHANNEL_ID}")
        print(f"   Target: {config.TARGET_TWITTER_USERNAME}")
        return True
    except Exception as e:
        print(f"❌ Config error: {e}")
        return False

def test_translation():
    """Test translation engine"""
    print("\n🔄 Testing Translation Engine...")
    try:
        from config import get_config
        from src.translation_engine import TranslationEngine
        
        config = get_config('production')
        engine = TranslationEngine(config)
        
        # Test simple translation
        sample_text = "Federal Reserve raises interest rates by 0.25%"
        translated = engine.translate_text(sample_text)
        
        print(f"Original: {sample_text}")
        print(f"Translated: {translated}")
        
        return True
    except Exception as e:
        print(f"❌ Translation error: {e}")
        return False

def test_news_analyzer():
    """Test news analyzer"""
    print("\n📊 Testing News Analyzer...")
    try:
        from config import get_config
        from src.news_analyzer import NewsAnalyzer
        
        config = get_config('production')
        analyzer = NewsAnalyzer(config)
        
        original_text = "Fed Chair Powell signals hawkish stance on inflation"
        translated_text = "رئیس فدرال رزرو پاول موضع سخت‌گیرانه در مورد تورم اعلام کرد"
        
        analysis = analyzer.analyze_news(original_text, translated_text)
        
        print(f"Analysis sentiment: {analysis.get('sentiment', 'unknown')}")
        print(f"Analysis text: {analysis.get('analysis', 'No analysis')[:100]}...")
        
        return True
    except Exception as e:
        print(f"❌ News analyzer error: {e}")
        return False

def test_message_formatter():
    """Test message formatter"""
    print("\n📝 Testing Message Formatter...")
    try:
        from config import get_config
        from src.message_formatter import MessageFormatter
        from datetime import datetime
        
        config = get_config('production')
        formatter = MessageFormatter(config)
        
        # Sample data
        original_text = "Fed raises rates 0.25%, Powell signals hawkish outlook"
        translated_text = "فدرال رزرو نرخ بهره را ۰.۲۵% افزایش داد، پاول چشم‌انداز سخت‌گیرانه اعلام کرد"
        
        analysis_data = {
            'analysis': '📊 *تحلیل سریع برای تریدرها:*\n• *فارکس (EUR/USD):* فشار نزولی - تقویت دلار\n• *طلا (XAU/USD):* فشار نزولی',
            'sentiment': 'hawkish',
            'confidence': 0.85,
            'key_info': {'financial_terms': ['Fed'], 'market_symbols': []}
        }
        
        tweet_data = {
            'id': '123456',
            'username': 'financialjuice',
            'url': 'https://twitter.com/financialjuice/status/123456',
            'created_at': datetime.now()
        }
        
        formatted_message = formatter.create_professional_message(
            original_text, translated_text, analysis_data, tweet_data
        )
        
        print("Formatted message:")
        print("-" * 40)
        print(formatted_message)
        print("-" * 40)
        
        return True
    except Exception as e:
        print(f"❌ Message formatter error: {e}")
        return False

async def test_telegram_bot():
    """Test telegram bot"""
    print("\n📱 Testing Telegram Bot...")
    try:
        from config import get_config
        from src.telegram_bot import TelegramBot
        
        config = get_config('production')
        bot = TelegramBot(config)
        
        # Test connection
        connection_ok = await bot.test_connection()
        
        if connection_ok:
            print("✅ Telegram connection successful")
            
            # Test sending a message
            test_message = """🤖 *تست ربات بهبود یافته*

📝 *ترجمه فارسی:*
این پیام تست برای بررسی عملکرد ربات بهبود یافته است

📊 *تحلیل سریع برای تریدرها:*
• *وضعیت:* ربات آماده و عملیاتی
• *عملکرد:* تمام سیستم‌ها فعال

📢 @FinancialJuiceFarsi"""
            
            result = await bot.send_message(test_message)
            
            if result:
                print("✅ Test message sent successfully")
                return True
            else:
                print("❌ Failed to send test message")
                return False
        else:
            print("❌ Telegram connection failed")
            return False
            
    except Exception as e:
        print(f"❌ Telegram bot error: {e}")
        return False

async def main():
    """Main test function"""
    print("🚀 Testing Fixed Bot Components")
    print("=" * 50)
    
    tests = [
        ("Config", test_config),
        ("Translation", test_translation),
        ("News Analyzer", test_news_analyzer),
        ("Message Formatter", test_message_formatter),
        ("Telegram Bot", test_telegram_bot)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n🔍 Running {test_name} test...")
        try:
            if asyncio.iscoroutinefunction(test_func):
                result = await test_func()
            else:
                result = test_func()
            results.append(result)
            status = "✅ PASS" if result else "❌ FAIL"
            print(f"Result: {status}")
        except Exception as e:
            print(f"❌ Test {test_name} crashed: {e}")
            results.append(False)
    
    print("\n" + "=" * 50)
    print("📊 Test Results:")
    
    for i, (test_name, _) in enumerate(tests):
        status = "✅ PASS" if results[i] else "❌ FAIL"
        print(f"  {test_name}: {status}")
    
    passed = sum(results)
    total = len(results)
    
    print(f"\n🎯 Summary: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Bot is ready!")
        print("\n📝 Next steps:")
        print("1. Make sure bot is admin in @FinancialJuiceFarsi")
        print("2. Run: python main.py")
    else:
        print("⚠️ Some tests failed. Check errors above.")
    
    return passed == total

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
