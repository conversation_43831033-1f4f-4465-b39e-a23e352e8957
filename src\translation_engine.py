"""
Translation Engine Module for Financial News Translation Bot
Handles intelligent translation with financial terms preservation
Uses FREE translation services only - NO PAID APIs
"""
import json
import re
import requests
from typing import Dict, List, Tuple, Optional
try:
    from deep_translator import GoogleTranslator
    DEEP_TRANSLATOR_AVAILABLE = True
    GOOGLETRANS_AVAILABLE = False  # Use deep_translator instead
except ImportError:
    try:
        from googletrans import Translator
        GOOGLETRANS_AVAILABLE = True
        DEEP_TRANSLATOR_AVAILABLE = False
    except ImportError:
        GOOGLETRANS_AVAILABLE = False
        DEEP_TRANSLATOR_AVAILABLE = False
        print("Warning: No translation library available, using basic translation")

try:
    from loguru import logger
    LOGURU_AVAILABLE = True
except ImportError:
    LOGURU_AVAILABLE = False
    import logging
    logger = logging.getLogger(__name__)

from config import Config

class TranslationEngine:
    """Intelligent translation engine with financial terms support"""
    
    def __init__(self, config: Config):
        self.config = config
        self.financial_terms = {}
        self.market_symbols = {}
        self.emojis = {}

        # FREE Translation Services Setup
        self.backup_translator = None
        if DEEP_TRANSLATOR_AVAILABLE:
            try:
                self.backup_translator = GoogleTranslator(source='en', target='fa')
                if LOGURU_AVAILABLE:
                    logger.info("Deep Translator (Google) initialized - FREE")
                else:
                    print("Deep Translator (Google) initialized - FREE")
            except Exception as e:
                if LOGURU_AVAILABLE:
                    logger.warning(f"Deep Translator failed: {e}")
                else:
                    print(f"Deep Translator failed: {e}")
        elif GOOGLETRANS_AVAILABLE:
            try:
                self.backup_translator = Translator()
                if LOGURU_AVAILABLE:
                    logger.info("Google Translate initialized")
                else:
                    print("Google Translate initialized")
            except Exception as e:
                if LOGURU_AVAILABLE:
                    logger.warning(f"Google Translate failed: {e}")
                else:
                    print(f"Google Translate failed: {e}")

        # Simple translation fallback
        self.simple_translations = {
            'Federal Reserve': 'فدرال رزرو',
            'interest rate': 'نرخ بهره',
            'inflation': 'تورم',
            'FOMC': 'کمیته بازار باز فدرال',
            'hawkish': 'سخت‌گیرانه',
            'dovish': 'نرم',
            'GDP': 'تولید ناخالص داخلی'
        }

        self.load_financial_terms()
    
    def load_financial_terms(self):
        """Load financial terms dictionary"""
        try:
            with open(self.config.FINANCIAL_TERMS_FILE, 'r', encoding='utf-8') as f:
                data = json.load(f)
                self.financial_terms = data.get('financial_terms', {})
                self.market_symbols = data.get('market_symbols', {})
                self.emojis = data.get('emojis', {})
            
            if LOGURU_AVAILABLE:
                logger.info(f"Loaded {len(self.financial_terms)} financial terms")
            else:
                print(f"Loaded {len(self.financial_terms)} financial terms")

        except Exception as e:
            if LOGURU_AVAILABLE:
                logger.error(f"Error loading financial terms: {e}")
            else:
                print(f"Error loading financial terms: {e}")
            self.financial_terms = {}
            self.market_symbols = {}
            self.emojis = {}
    
    def preprocess_text(self, text: str) -> Tuple[str, Dict[str, str]]:
        """Preprocess text to protect financial terms and symbols"""
        protected_terms = {}
        processed_text = text
        
        # Protect market symbols (e.g., $TSLA, $AAPL)
        symbol_pattern = r'\$[A-Z]{1,5}'
        symbols = re.findall(symbol_pattern, text)
        for i, symbol in enumerate(symbols):
            placeholder = f"__SYMBOL_{i}__"
            protected_terms[placeholder] = symbol
            processed_text = processed_text.replace(symbol, placeholder)
        
        # Protect financial terms
        for term, translation in self.financial_terms.items():
            if term.lower() in text.lower():
                placeholder = f"__TERM_{len(protected_terms)}__"
                protected_terms[placeholder] = term
                # Case-insensitive replacement
                pattern = re.compile(re.escape(term), re.IGNORECASE)
                processed_text = pattern.sub(placeholder, processed_text)
        
        # Protect numbers with % and currency symbols
        number_pattern = r'[\d,]+\.?\d*\s*%|[\d,]+\.?\d*\s*\$|[\d,]+\.?\d*\s*€'
        numbers = re.findall(number_pattern, text)
        for i, number in enumerate(numbers):
            placeholder = f"__NUMBER_{i}__"
            protected_terms[placeholder] = number
            processed_text = processed_text.replace(number, placeholder)
        
        return processed_text, protected_terms
    
    def postprocess_text(self, translated_text: str, protected_terms: Dict[str, str]) -> str:
        """Restore protected terms in translated text"""
        result = translated_text
        
        for placeholder, original_term in protected_terms.items():
            if placeholder.startswith('__SYMBOL_'):
                # Replace market symbols with Persian equivalent if available
                persian_symbol = self.market_symbols.get(original_term, original_term)
                result = result.replace(placeholder, persian_symbol)
            elif placeholder.startswith('__TERM_'):
                # Replace with Persian financial term
                persian_term = self.financial_terms.get(original_term, original_term)
                result = result.replace(placeholder, persian_term)
            elif placeholder.startswith('__NUMBER_'):
                # Convert numbers to Persian format
                persian_number = self.convert_numbers_to_persian(original_term)
                result = result.replace(placeholder, persian_number)
            else:
                result = result.replace(placeholder, original_term)
        
        return result
    
    def convert_numbers_to_persian(self, text: str) -> str:
        """Convert English numbers to Persian"""
        english_to_persian = {
            '0': '۰', '1': '۱', '2': '۲', '3': '۳', '4': '۴',
            '5': '۵', '6': '۶', '7': '۷', '8': '۸', '9': '۹'
        }
        
        result = text
        for eng, per in english_to_persian.items():
            result = result.replace(eng, per)
        
        return result
    
    def translate_with_free_ai(self, text: str) -> Optional[str]:
        """Translate using FREE AI services (Hugging Face, etc.)"""
        try:
            # Try Hugging Face free translation API
            url = "https://api-inference.huggingface.co/models/Helsinki-NLP/opus-mt-en-fa"
            headers = {"Authorization": "Bearer hf_demo"}  # Free demo token

            payload = {"inputs": text}
            response = requests.post(url, headers=headers, json=payload, timeout=10)

            if response.status_code == 200:
                result = response.json()
                if isinstance(result, list) and len(result) > 0:
                    translation = result[0].get('translation_text', '')
                    if translation:
                        logger.debug(f"HuggingFace translation: {translation}")
                        return translation

            logger.warning("HuggingFace translation failed, trying alternative...")
            return None

        except Exception as e:
            logger.error(f"Free AI translation error: {e}")
            return None

    def translate_with_mymemory(self, text: str) -> Optional[str]:
        """Translate using MyMemory (free service)"""
        try:
            translation = self.mymemory_translator.translate(text)
            logger.debug(f"MyMemory translation: {translation}")
            return translation

        except Exception as e:
            logger.warning(f"MyMemory translation error: {e}")
            return None
    
    def translate_with_google(self, text: str) -> Optional[str]:
        """Translate using Google Translate"""
        try:
            translation = self.google_translator.translate(text)
            logger.debug(f"Google translation: {translation}")
            return translation
            
        except Exception as e:
            logger.warning(f"Google Translate error: {e}")
            try:
                # Fallback to backup translator
                translation = self.backup_translator.translate(text, src='en', dest='fa')
                return translation.text
            except Exception as e2:
                logger.error(f"Backup translation error: {e2}")
                return None
    
    def translate_text(self, text: str) -> str:
        """Main translation function with intelligent processing"""
        try:
            if LOGURU_AVAILABLE:
                logger.info(f"Translating text: {text[:100]}...")
            else:
                print(f"Translating text: {text[:100]}...")

            # Simple translation approach
            translation = self.simple_translate(text)

            if not translation:
                if LOGURU_AVAILABLE:
                    logger.error("Translation failed")
                else:
                    print("Translation failed")
                return text  # Return original text if translation fails

            # Clean up the translation
            final_translation = self.clean_translation(translation)

            if LOGURU_AVAILABLE:
                logger.info(f"Translation completed: {final_translation[:100]}...")
            else:
                print(f"Translation completed: {final_translation[:100]}...")
            return final_translation

        except Exception as e:
            if LOGURU_AVAILABLE:
                logger.error(f"Translation error: {e}")
            else:
                print(f"Translation error: {e}")
            return text

    def simple_translate(self, text: str) -> str:
        """Simple translation using available FREE services"""
        # Try Deep Translator first (more reliable)
        if DEEP_TRANSLATOR_AVAILABLE:
            try:
                translator = GoogleTranslator(source='en', target='fa')
                result = translator.translate(text)
                if result:
                    if LOGURU_AVAILABLE:
                        logger.debug("Deep Translator succeeded")
                    return result
            except Exception as e:
                if LOGURU_AVAILABLE:
                    logger.warning(f"Deep Translator failed: {e}")
                else:
                    print(f"Deep Translator failed: {e}")

        # Try Google Translate as backup
        if self.backup_translator and GOOGLETRANS_AVAILABLE:
            try:
                result = self.backup_translator.translate(text, src='en', dest='fa')
                if result and result.text:
                    return result.text
            except Exception as e:
                if LOGURU_AVAILABLE:
                    logger.warning(f"Google Translate failed: {e}")
                else:
                    print(f"Google Translate failed: {e}")

        # Fallback to simple word replacement
        return self.basic_word_replacement(text)

    def basic_word_replacement(self, text: str) -> str:
        """Basic word-by-word replacement for financial terms"""
        result = text

        # Replace common financial terms
        for english, persian in self.simple_translations.items():
            result = result.replace(english, persian)
            result = result.replace(english.lower(), persian)
            result = result.replace(english.upper(), persian)

        # Replace numbers with Persian equivalents
        result = self.convert_numbers_to_persian(result)

        return result
    
    def clean_translation(self, text: str) -> str:
        """Clean and improve translation quality"""
        # Remove extra spaces
        text = re.sub(r'\s+', ' ', text).strip()
        
        # Fix common translation issues
        replacements = {
            'درصد': '%',
            'دلار آمریکا': 'دلار',
            'ایالات متحده آمریکا': 'آمریکا',
            'بازار سهام': 'بازار',
        }
        
        for old, new in replacements.items():
            text = text.replace(old, new)
        
        return text
    
    def get_translation_confidence(self, original: str, translated: str) -> float:
        """Calculate translation confidence score"""
        try:
            # Simple confidence calculation based on length ratio and term preservation
            length_ratio = len(translated) / len(original) if len(original) > 0 else 0
            
            # Check if financial terms are preserved
            financial_terms_found = 0
            total_financial_terms = 0
            
            for term in self.financial_terms.keys():
                if term.lower() in original.lower():
                    total_financial_terms += 1
                    if self.financial_terms[term] in translated:
                        financial_terms_found += 1
            
            term_preservation_score = (
                financial_terms_found / total_financial_terms 
                if total_financial_terms > 0 else 1.0
            )
            
            # Combine scores
            confidence = (
                min(length_ratio, 1.0) * 0.3 +  # Length similarity
                term_preservation_score * 0.7    # Term preservation
            )
            
            return min(confidence, 1.0)
            
        except Exception as e:
            logger.error(f"Error calculating confidence: {e}")
            return 0.5
