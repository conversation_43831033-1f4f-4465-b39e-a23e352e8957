"""
Telegram Bot Module for Financial News Translation Bot
Handles message publishing to Telegram channels
"""
import asyncio
import io
import base64
import requests
from datetime import datetime
from typing import Optional, Dict, List

try:
    from telegram import Bo<PERSON>, InputMediaPhoto
    from telegram.constants import ParseMode
    from telegram.error import TelegramError, RetryAfter, TimedOut
    TELEGRAM_LIB_AVAILABLE = True
except ImportError:
    TELEGRAM_LIB_AVAILABLE = False
    print("Warning: python-telegram-bot not available, using basic HTTP requests")

try:
    from loguru import logger
    LOGURU_AVAILABLE = True
except ImportError:
    LOGURU_AVAILABLE = False
    import logging
    logger = logging.getLogger(__name__)

from config import Config

class TelegramBot:
    """Telegram bot for publishing financial news"""
    
    def __init__(self, config: Config):
        self.config = config
        self.bot = None
        self.bot_token = config.TELEGRAM_BOT_TOKEN
        self.channel_id = config.TELEGRAM_CHANNEL_ID
        self.base_url = f"https://api.telegram.org/bot{self.bot_token}"
        self.setup_bot()
    
    def setup_bot(self):
        """Initialize Telegram bot"""
        try:
            if not self.bot_token:
                raise ValueError("Telegram bot token not provided")

            if TELEGRAM_LIB_AVAILABLE:
                self.bot = Bot(token=self.bot_token)
                if LOGURU_AVAILABLE:
                    logger.info("Telegram bot initialized successfully")
                else:
                    print("Telegram bot initialized successfully")
            else:
                if LOGURU_AVAILABLE:
                    logger.info("Using HTTP requests for Telegram API")
                else:
                    print("Using HTTP requests for Telegram API")

        except Exception as e:
            if LOGURU_AVAILABLE:
                logger.error(f"Failed to setup Telegram bot: {e}")
            else:
                print(f"Failed to setup Telegram bot: {e}")
            raise
    
    async def test_connection(self) -> bool:
        """Test bot connection and permissions"""
        try:
            # Test bot info
            bot_info = await self.bot.get_me()
            logger.info(f"Bot connected: @{bot_info.username}")
            
            # Test channel access
            if self.channel_id:
                try:
                    chat = await self.bot.get_chat(self.channel_id)
                    logger.info(f"Channel access confirmed: {chat.title}")
                    return True
                except Exception as e:
                    logger.error(f"Cannot access channel {self.channel_id}: {e}")
                    return False
            
            return True
            
        except Exception as e:
            logger.error(f"Bot connection test failed: {e}")
            return False
    
    async def send_message(
        self,
        text: str,
        parse_mode: str = "Markdown",
        disable_web_page_preview: bool = False,
        pin_message: bool = False
    ) -> Optional[Dict]:
        """Send text message to channel"""
        try:
            if TELEGRAM_LIB_AVAILABLE and self.bot:
                return await self._send_message_with_lib(text, parse_mode, disable_web_page_preview, pin_message)
            else:
                return await self._send_message_with_http(text, parse_mode, disable_web_page_preview, pin_message)

        except Exception as e:
            if LOGURU_AVAILABLE:
                logger.error(f"Send message error: {e}")
            else:
                print(f"Send message error: {e}")
            return None

    async def _send_message_with_lib(self, text, parse_mode, disable_web_page_preview, pin_message):
        """Send message using telegram library"""
        if not self.bot or not self.channel_id:
            if LOGURU_AVAILABLE:
                logger.error("Bot or channel not configured")
            else:
                print("Bot or channel not configured")
            return None

        # Send message
        message = await self.bot.send_message(
            chat_id=self.channel_id,
            text=text,
            parse_mode=parse_mode,
            disable_web_page_preview=disable_web_page_preview
        )

        if LOGURU_AVAILABLE:
            logger.info(f"Message sent successfully: {message.message_id}")
        else:
            print(f"Message sent successfully: {message.message_id}")

        return {
            'message_id': message.message_id,
            'date': message.date,
            'text': message.text
        }

    async def _send_message_with_http(self, text, parse_mode, disable_web_page_preview, pin_message):
        """Send message using HTTP requests"""
        import requests

        url = f"{self.base_url}/sendMessage"
        params = {
            'chat_id': self.channel_id,
            'text': text,
            'parse_mode': parse_mode,
            'disable_web_page_preview': disable_web_page_preview
        }

        response = requests.post(url, params=params, timeout=15)

        if response.status_code == 200:
            data = response.json()
            if data.get('ok'):
                message_info = data.get('result', {})
                if LOGURU_AVAILABLE:
                    logger.info(f"Message sent successfully: {message_info.get('message_id')}")
                else:
                    print(f"Message sent successfully: {message_info.get('message_id')}")
                return {
                    'message_id': message_info.get('message_id'),
                    'date': message_info.get('date'),
                    'text': message_info.get('text')
                }
            else:
                if LOGURU_AVAILABLE:
                    logger.error(f"Telegram API error: {data.get('description')}")
                else:
                    print(f"Telegram API error: {data.get('description')}")
                return None
        else:
            if LOGURU_AVAILABLE:
                logger.error(f"HTTP error: {response.status_code}")
            else:
                print(f"HTTP error: {response.status_code}")
            return None
    
    async def send_photo(
        self,
        photo_data: bytes,
        caption: str = "",
        parse_mode: str = ParseMode.MARKDOWN
    ) -> Optional[Dict]:
        """Send photo with caption to channel"""
        try:
            if not self.bot or not self.channel_id:
                logger.error("Bot or channel not configured")
                return None
            
            # Create photo file object
            photo_file = io.BytesIO(photo_data)
            photo_file.name = f"screenshot_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
            
            message = await self.bot.send_photo(
                chat_id=self.channel_id,
                photo=photo_file,
                caption=caption,
                parse_mode=parse_mode
            )
            
            logger.info(f"Photo sent successfully: {message.message_id}")
            return {
                'message_id': message.message_id,
                'date': message.date,
                'caption': message.caption
            }
            
        except Exception as e:
            logger.error(f"Error sending photo: {e}")
            return None
    
    async def send_media_group(
        self,
        media_list: List[Dict],
        caption: str = ""
    ) -> Optional[List[Dict]]:
        """Send multiple media items as a group"""
        try:
            if not self.bot or not self.channel_id:
                logger.error("Bot or channel not configured")
                return None
            
            media_group = []
            for i, media_item in enumerate(media_list):
                if media_item['type'] == 'photo':
                    photo_file = io.BytesIO(media_item['data'])
                    photo_file.name = f"image_{i}.png"
                    
                    input_media = InputMediaPhoto(
                        media=photo_file,
                        caption=caption if i == 0 else "",  # Only first item gets caption
                        parse_mode=ParseMode.MARKDOWN
                    )
                    media_group.append(input_media)
            
            if not media_group:
                logger.warning("No valid media items to send")
                return None
            
            messages = await self.bot.send_media_group(
                chat_id=self.channel_id,
                media=media_group
            )
            
            logger.info(f"Media group sent successfully: {len(messages)} items")
            return [
                {
                    'message_id': msg.message_id,
                    'date': msg.date
                }
                for msg in messages
            ]
            
        except Exception as e:
            logger.error(f"Error sending media group: {e}")
            return None
    
    async def edit_message(
        self,
        message_id: int,
        new_text: str,
        parse_mode: str = ParseMode.MARKDOWN
    ) -> bool:
        """Edit existing message"""
        try:
            if not self.bot or not self.channel_id:
                logger.error("Bot or channel not configured")
                return False
            
            await self.bot.edit_message_text(
                chat_id=self.channel_id,
                message_id=message_id,
                text=new_text,
                parse_mode=parse_mode
            )
            
            logger.info(f"Message {message_id} edited successfully")
            return True
            
        except Exception as e:
            logger.error(f"Error editing message {message_id}: {e}")
            return False
    
    async def delete_message(self, message_id: int) -> bool:
        """Delete message"""
        try:
            if not self.bot or not self.channel_id:
                logger.error("Bot or channel not configured")
                return False
            
            await self.bot.delete_message(
                chat_id=self.channel_id,
                message_id=message_id
            )
            
            logger.info(f"Message {message_id} deleted successfully")
            return True
            
        except Exception as e:
            logger.error(f"Error deleting message {message_id}: {e}")
            return False
    
    async def get_channel_info(self) -> Optional[Dict]:
        """Get channel information"""
        try:
            if not self.bot or not self.channel_id:
                return None
            
            chat = await self.bot.get_chat(self.channel_id)
            
            return {
                'id': chat.id,
                'title': chat.title,
                'username': chat.username,
                'type': chat.type,
                'member_count': getattr(chat, 'member_count', None)
            }
            
        except Exception as e:
            logger.error(f"Error getting channel info: {e}")
            return None
    
    async def send_formatted_news(
        self,
        formatted_message: str,
        screenshot_data: Optional[bytes] = None,
        pin_important: bool = False
    ) -> Optional[Dict]:
        """Send formatted news message with optional screenshot"""
        try:
            result = None
            
            if screenshot_data and self.config.INCLUDE_SCREENSHOT:
                # Send as photo with caption
                result = await self.send_photo(
                    photo_data=screenshot_data,
                    caption=formatted_message,
                    parse_mode=ParseMode.MARKDOWN
                )
            else:
                # Send as text message
                result = await self.send_message(
                    text=formatted_message,
                    parse_mode=ParseMode.MARKDOWN,
                    disable_web_page_preview=True,
                    pin_message=pin_important
                )
            
            return result
            
        except Exception as e:
            logger.error(f"Error sending formatted news: {e}")
            return None
    
    async def send_error_notification(self, error_message: str) -> Optional[Dict]:
        """Send error notification to channel (for debugging)"""
        try:
            if not self.config.DEBUG:
                return None
            
            error_text = f"⚠️ *خطای ربات:*\n\n{error_message}\n\n⏰ {datetime.now().strftime('%H:%M:%S')}"
            
            return await self.send_message(
                text=error_text,
                parse_mode=ParseMode.MARKDOWN,
                disable_web_page_preview=True
            )
            
        except Exception as e:
            logger.error(f"Error sending error notification: {e}")
            return None
    
    def validate_message_format(self, text: str) -> bool:
        """Validate message format and length"""
        try:
            # Check length
            if len(text) > 4096:
                logger.warning(f"Message too long: {len(text)} characters")
                return False
            
            # Check for basic markdown issues
            if text.count('*') % 2 != 0:
                logger.warning("Unmatched markdown asterisks")
                return False
            
            return True
            
        except Exception as e:
            if LOGURU_AVAILABLE:
                logger.error(f"Error validating message format: {e}")
            else:
                print(f"Error validating message format: {e}")
            return False

    async def send_formatted_news(
        self,
        formatted_message: str,
        screenshot_data: Optional[bytes] = None,
        pin_important: bool = False
    ) -> bool:
        """Send formatted news message"""
        try:
            # Send the formatted message
            result = await self.send_message(
                text=formatted_message,
                parse_mode="Markdown",
                disable_web_page_preview=True,
                pin_message=pin_important
            )

            return result is not None

        except Exception as e:
            if LOGURU_AVAILABLE:
                logger.error(f"Error sending formatted news: {e}")
            else:
                print(f"Error sending formatted news: {e}")
            return False
