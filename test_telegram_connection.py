"""
Test Telegram bot connection and send a sample message
"""
import asyncio
import sys
import os

# Add src directory to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

async def test_telegram_bot():
    """Test Telegram bot connection"""
    try:
        from config import get_config
        from src.telegram_bot import TelegramBot
        
        print("🤖 Testing Telegram Bot Connection")
        print("=" * 50)
        
        # Load config
        config = get_config('production')
        print(f"✅ Config loaded")
        print(f"   Bot Token: {config.TELEGRAM_BOT_TOKEN[:20]}...")
        print(f"   Channel: {config.TELEGRAM_CHANNEL_ID}")
        
        # Initialize bot
        bot = TelegramBot(config)
        print("✅ Bot initialized")
        
        # Test connection
        print("\n🔗 Testing connection...")
        connection_ok = await bot.test_connection()
        
        if connection_ok:
            print("✅ Connection successful!")
            
            # Send test message
            print("\n📤 Sending test message...")
            test_message = """🤖 *تست ربات مترجم اخبار مالی*

📝 *ترجمه فارسی:*
این یک پیام تست است برای بررسی عملکرد ربات

📌 *پ.ن:*
*تست:* این پیام برای اطمینان از عملکرد صحیح ربات ارسال شده

📊 *تحلیل سریع برای تریدرها:*
• *وضعیت:* ربات آماده و عملیاتی است
• *عملکرد:* تمام سیستم‌ها فعال

📢 @FinancialJuiceFarsi"""
            
            result = await bot.send_message(test_message)
            
            if result:
                print("✅ Test message sent successfully!")
                print(f"   Message ID: {result.get('message_id')}")
                return True
            else:
                print("❌ Failed to send test message")
                return False
        else:
            print("❌ Connection failed")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

async def test_sample_financial_post():
    """Test a sample financial news post"""
    try:
        from config import get_config
        from src.telegram_bot import TelegramBot
        from src.message_formatter import MessageFormatter
        from src.screenshot_generator import ScreenshotGenerator
        from datetime import datetime
        
        print("\n📰 Testing Sample Financial Post")
        print("=" * 50)
        
        config = get_config('production')
        bot = TelegramBot(config)
        formatter = MessageFormatter(config)
        screenshot_gen = ScreenshotGenerator(config)
        
        # Sample data
        original_text = "BREAKING: Fed Chair Powell signals 0.25% rate hike at next FOMC meeting, citing persistent inflation concerns"
        translated_text = "فوری: رئیس فدرال رزرو پاول افزایش ۰.۲۵% نرخ بهره در جلسه بعدی FOMC را با اشاره به نگرانی‌های تورمی اعلام کرد"
        
        analysis_data = {
            'analysis': '📊 *تحلیل سریع برای تریدرها:*\n• *فارکس (EUR/USD):* فشار نزولی - تقویت احتمالی دلار\n• *فارکس (GBP/USD):* احتمال کاهش - دلار قوی‌تر\n• *طلا (XAU/USD):* فشار نزولی - رقابت با دلار قوی\n• *شاخص‌ها (S&P 500):* احتمال رشد - بهبود چشم‌انداز\n\n*زمینه اقتصادی:* تصمیمات فدرال رزرو بر تمام بازارهای جهانی تأثیرگذار است',
            'sentiment': 'hawkish',
            'confidence': 0.85,
            'key_info': {
                'financial_terms': ['FOMC', 'Fed'],
                'market_symbols': []
            }
        }
        
        tweet_data = {
            'id': '1234567890',
            'username': 'financialjuice',
            'url': 'https://twitter.com/financialjuice/status/1234567890',
            'created_at': datetime.now()
        }
        
        # Format message
        formatted_message = formatter.create_professional_message(
            original_text, translated_text, analysis_data, tweet_data
        )
        
        print("📝 Formatted message:")
        print("-" * 30)
        print(formatted_message)
        print("-" * 30)
        
        # Generate screenshot
        screenshot_data = screenshot_gen.create_placeholder_image()
        
        # Send with screenshot
        print("\n📤 Sending formatted post with screenshot...")
        
        result = await bot.send_formatted_news(
            formatted_message=formatted_message,
            screenshot_data=screenshot_data,
            pin_important=False
        )
        
        if result:
            print("✅ Sample financial post sent successfully!")
            return True
        else:
            print("❌ Failed to send sample post")
            return False
            
    except Exception as e:
        print(f"❌ Error in sample post: {e}")
        return False

async def main():
    """Main test function"""
    print("🚀 Telegram Bot Test Suite")
    print("=" * 60)
    
    # Test 1: Basic connection
    test1_result = await test_telegram_bot()
    
    if test1_result:
        print("\n" + "=" * 60)
        
        # Test 2: Sample financial post
        test2_result = await test_sample_financial_post()
        
        print("\n" + "=" * 60)
        print("📊 Test Results:")
        print(f"   Basic Connection: {'✅ PASS' if test1_result else '❌ FAIL'}")
        print(f"   Sample Post: {'✅ PASS' if test2_result else '❌ FAIL'}")
        
        if test1_result and test2_result:
            print("\n🎉 All tests passed! Bot is ready for production!")
            print("\n📝 Next steps:")
            print("1. Check your Telegram channel @FinancialJuiceFarsi")
            print("2. Verify the test messages were received")
            print("3. Run: python main.py to start monitoring")
        else:
            print("\n⚠️ Some tests failed. Please check the errors above.")
    else:
        print("\n❌ Basic connection failed. Please check:")
        print("1. Bot token is correct")
        print("2. Bot is added to the channel as admin")
        print("3. Channel ID is correct (@FinancialJuiceFarsi)")

if __name__ == "__main__":
    asyncio.run(main())
