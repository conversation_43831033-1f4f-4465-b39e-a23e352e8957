"""
Basic Telegram bot test without complex dependencies
"""
import requests
import json

def test_telegram_bot():
    """Test Telegram bot with basic HTTP requests"""
    
    # Bot configuration
    BOT_TOKEN = "8207363120:AAHL0OJoGCgehj3eQEF1Kh6HJmREKhe-vvw"
    CHANNEL_ID = "@FinancialJuiceFarsi"
    
    print("🤖 Testing Telegram Bot Connection")
    print("=" * 50)
    print(f"Bot Token: {BOT_TOKEN[:20]}...")
    print(f"Channel: {CHANNEL_ID}")
    print()
    
    # Test 1: Get bot info
    print("🔍 Test 1: Getting bot information...")
    try:
        url = f"https://api.telegram.org/bot{BOT_TOKEN}/getMe"
        response = requests.get(url, timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            if data.get('ok'):
                bot_info = data.get('result', {})
                print(f"✅ Bot connected successfully!")
                print(f"   Bot Name: {bot_info.get('first_name', 'Unknown')}")
                print(f"   Username: @{bot_info.get('username', 'Unknown')}")
                print(f"   Bot ID: {bot_info.get('id', 'Unknown')}")
            else:
                print(f"❌ Bot API error: {data.get('description', 'Unknown error')}")
                return False
        else:
            print(f"❌ HTTP error: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Connection error: {e}")
        return False
    
    # Test 2: Check channel access
    print("\n🔍 Test 2: Checking channel access...")
    try:
        url = f"https://api.telegram.org/bot{BOT_TOKEN}/getChat"
        params = {'chat_id': CHANNEL_ID}
        response = requests.get(url, params=params, timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            if data.get('ok'):
                chat_info = data.get('result', {})
                print(f"✅ Channel access confirmed!")
                print(f"   Channel Title: {chat_info.get('title', 'Unknown')}")
                print(f"   Channel Type: {chat_info.get('type', 'Unknown')}")
                print(f"   Member Count: {chat_info.get('members_count', 'Unknown')}")
            else:
                print(f"❌ Channel access error: {data.get('description', 'Unknown error')}")
                print("   Make sure:")
                print("   1. Bot is added to the channel")
                print("   2. Bot has admin permissions")
                print("   3. Channel username is correct")
                return False
        else:
            print(f"❌ HTTP error: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Channel access error: {e}")
        return False
    
    # Test 3: Send test message
    print("\n🔍 Test 3: Sending test message...")
    try:
        test_message = """🤖 *تست ربات مترجم اخبار مالی*

📝 *ترجمه فارسی:*
این یک پیام تست است برای بررسی عملکرد ربات

📌 *پ.ن:*
*تست:* این پیام برای اطمینان از عملکرد صحیح ربات ارسال شده

📊 *تحلیل سریع برای تریدرها:*
• *وضعیت:* ربات آماده و عملیاتی است
• *عملکرد:* تمام سیستم‌ها فعال

📢 @FinancialJuiceFarsi"""
        
        url = f"https://api.telegram.org/bot{BOT_TOKEN}/sendMessage"
        params = {
            'chat_id': CHANNEL_ID,
            'text': test_message,
            'parse_mode': 'Markdown'
        }
        
        response = requests.post(url, params=params, timeout=15)
        
        if response.status_code == 200:
            data = response.json()
            if data.get('ok'):
                message_info = data.get('result', {})
                print(f"✅ Test message sent successfully!")
                print(f"   Message ID: {message_info.get('message_id', 'Unknown')}")
                print(f"   Date: {message_info.get('date', 'Unknown')}")
                print()
                print("🎉 All tests passed! Bot is ready for production!")
                return True
            else:
                print(f"❌ Send message error: {data.get('description', 'Unknown error')}")
                return False
        else:
            print(f"❌ HTTP error: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Send message error: {e}")
        return False

def test_sample_financial_message():
    """Send a sample financial news message"""
    
    BOT_TOKEN = "8207363120:AAHL0OJoGCgehj3eQEF1Kh6HJmREKhe-vvw"
    CHANNEL_ID = "@FinancialJuiceFarsi"
    
    print("\n📰 Test 4: Sending sample financial news...")
    
    sample_message = """📝 *ترجمه فارسی:*
فدرال رزرو نرخ بهره را ۰.۲۵% افزایش داد و پاول چشم‌انداز سخت‌گیرانه برای ۲۰۲۴ اعلام کرد

📌 *پ.ن (توضیح اصطلاحات):*
*FOMC:* کمیته بازار باز فدرال - تصمیم‌گیرنده نرخ بهره آمریکا
*Hawkish:* سیاست سخت‌گیرانه - احتمال افزایش نرخ بهره

📊 *تحلیل سریع برای تریدرها:*
• *فارکس (EUR/USD):* فشار نزولی - تقویت احتمالی دلار
• *فارکس (GBP/USD):* احتمال کاهش - دلار قوی‌تر
• *طلا (XAU/USD):* فشار نزولی - رقابت با دلار قوی
• *شاخص‌ها (S&P 500):* احتمال رشد - بهبود چشم‌انداز
• *ارزهای دیجیتال (BTC):* نوسان بالا - حساسیت به نرخ بهره

*زمینه اقتصادی:* تصمیمات فدرال رزرو بر تمام بازارهای جهانی تأثیرگذار است

📢 @FinancialJuiceFarsi"""
    
    try:
        url = f"https://api.telegram.org/bot{BOT_TOKEN}/sendMessage"
        params = {
            'chat_id': CHANNEL_ID,
            'text': sample_message,
            'parse_mode': 'Markdown'
        }
        
        response = requests.post(url, params=params, timeout=15)
        
        if response.status_code == 200:
            data = response.json()
            if data.get('ok'):
                print("✅ Sample financial news sent successfully!")
                print("   This is exactly how your posts will look!")
                return True
            else:
                print(f"❌ Error: {data.get('description', 'Unknown error')}")
                return False
        else:
            print(f"❌ HTTP error: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def main():
    """Main test function"""
    print("🚀 Telegram Bot Test Suite")
    print("Testing bot for @FinancialJuiceFarsi")
    print("Target Twitter: @financialjuice")
    print("=" * 60)
    
    # Run basic tests
    basic_test_result = test_telegram_bot()
    
    if basic_test_result:
        # Run sample message test
        sample_test_result = test_sample_financial_message()
        
        print("\n" + "=" * 60)
        print("📊 Test Results Summary:")
        print(f"   Basic Connection: {'✅ PASS' if basic_test_result else '❌ FAIL'}")
        print(f"   Sample Message: {'✅ PASS' if sample_test_result else '❌ FAIL'}")
        
        if basic_test_result and sample_test_result:
            print("\n🎉 ALL TESTS PASSED!")
            print("\n📝 Next Steps:")
            print("1. Check your channel @FinancialJuiceFarsi")
            print("2. Verify test messages were received")
            print("3. Run: python main.py to start monitoring @financialjuice")
            print("\n💰 Your bot is ready to make money!")
        else:
            print("\n⚠️ Some tests failed. Please check the errors above.")
    else:
        print("\n❌ Basic connection failed. Please check:")
        print("1. Bot token is correct")
        print("2. Bot is added to channel as admin")
        print("3. Channel username is correct")

if __name__ == "__main__":
    main()
