"""
Message Formatter Module for Financial News Translation <PERSON><PERSON>les message formatting, emojis, and styling for Telegram
"""
import json
import re
from datetime import datetime
from typing import Dict, List, Optional
from loguru import logger
from config import Config

class MessageFormatter:
    """Professional message formatter for Telegram posts"""
    
    def __init__(self, config: Config):
        self.config = config
        self.emojis = {}
        self.load_emojis()
    
    def load_emojis(self):
        """Load emoji mappings"""
        try:
            with open(self.config.FINANCIAL_TERMS_FILE, 'r', encoding='utf-8') as f:
                data = json.load(f)
                self.emojis = data.get('emojis', {})
            
            logger.info("Emojis loaded for message formatting")
            
        except Exception as e:
            logger.error(f"Error loading emojis: {e}")
            # Default emojis
            self.emojis = {
                'up': '📈', 'down': '📉', 'neutral': '➡️',
                'warning': '⚠️', 'fire': '🔥', 'rocket': '🚀',
                'money': '💰', 'chart': '📊', 'news': '📰',
                'analysis': '📊', 'translation': '🔄', 'pin': '📌',
                'time': '⏰', 'globe': '🌍'
            }
    
    def get_sentiment_emoji(self, sentiment: str) -> str:
        """Get emoji based on sentiment"""
        emoji_map = {
            'positive': self.emojis.get('up', '📈'),
            'negative': self.emojis.get('down', '📉'),
            'neutral': self.emojis.get('neutral', '➡️')
        }
        return emoji_map.get(sentiment, '📊')
    
    def format_title(self, original_text: str) -> str:
        """Format the title section"""
        # Truncate if too long
        if len(original_text) > 100:
            title = original_text[:97] + "..."
        else:
            title = original_text
        
        if self.config.USE_BOLD_TEXT:
            return f"*{title}*"
        return title
    
    def format_translation_section(self, translated_text: str) -> str:
        """Format the translation section"""
        translation_emoji = self.emojis.get('translation', '🔄')
        
        section = f"{translation_emoji} *ترجمه:*\n"
        section += f"{translated_text}"
        
        return section
    
    def format_analysis_section(self, analysis: str, sentiment: str) -> str:
        """Format the analysis section"""
        analysis_emoji = self.emojis.get('analysis', '📊')
        
        section = f"{analysis_emoji} *تحلیل:*\n"
        section += f"{analysis}"
        
        return section
    
    def format_footnote_section(self, key_info: Dict) -> str:
        """Format footnote with key terms and symbols"""
        if not key_info.get('financial_terms') and not key_info.get('market_symbols'):
            return ""
        
        footnote_parts = []
        
        # Add financial terms
        if key_info.get('financial_terms'):
            terms = key_info['financial_terms'][:3]  # Limit to 3 terms
            footnote_parts.append(f"*اصطلاحات کلیدی:* {', '.join(terms)}")
        
        # Add market symbols
        if key_info.get('market_symbols'):
            symbols = key_info['market_symbols'][:3]  # Limit to 3 symbols
            footnote_parts.append(f"*نمادها:* {', '.join(symbols)}")
        
        if footnote_parts:
            pin_emoji = self.emojis.get('pin', '📌')
            return f"\n{pin_emoji} *پ.ن:*\n" + "\n".join(footnote_parts)
        
        return ""
    
    def format_source_section(self, tweet_url: str, username: str) -> str:
        """Format source information"""
        globe_emoji = self.emojis.get('globe', '🌍')
        return f"\n{globe_emoji} *منبع:* [@{username}]({tweet_url})"
    
    def format_timestamp(self) -> str:
        """Format timestamp"""
        time_emoji = self.emojis.get('time', '⏰')
        now = datetime.now()
        persian_time = now.strftime("%H:%M")
        return f"\n{time_emoji} *زمان انتشار:* {persian_time}"
    
    def add_visual_separators(self, text: str) -> str:
        """Add visual separators between sections"""
        # Add subtle separators
        separator = "─" * 25
        sections = text.split('\n\n')
        return f"\n{separator}\n".join(sections)
    
    def format_numbers_persian(self, text: str) -> str:
        """Convert numbers to Persian in the final text"""
        if not text:
            return text
        
        english_to_persian = {
            '0': '۰', '1': '۱', '2': '۲', '3': '۳', '4': '۴',
            '5': '۵', '6': '۶', '7': '۷', '8': '۸', '9': '۹'
        }
        
        result = text
        for eng, per in english_to_persian.items():
            result = result.replace(eng, per)
        
        return result
    
    def clean_markdown(self, text: str) -> str:
        """Clean and validate Markdown formatting"""
        # Ensure proper bold formatting
        text = re.sub(r'\*([^*]+)\*', r'*\1*', text)
        
        # Remove excessive line breaks
        text = re.sub(r'\n{3,}', '\n\n', text)
        
        # Ensure proper spacing around emojis
        text = re.sub(r'([^\s])([📈📉📊🔄💰⚠️🔥🚀📌⏰🌍➡️📰])', r'\1 \2', text)
        text = re.sub(r'([📈📉📊🔄💰⚠️🔥🚀📌⏰🌍➡️📰])([^\s])', r'\1 \2', text)
        
        return text.strip()
    
    def create_professional_message(
        self,
        original_text: str,
        translated_text: str,
        analysis_data: Dict,
        tweet_data: Dict
    ) -> str:
        """Create message based on employer's EXACT format requirements"""
        try:
            message_parts = []

            # 1. اسکرین‌شات توییت (will be handled as photo caption)
            # Note: Screenshot will be sent as photo with this caption

            # 2. ترجمه فارسی توییت (روان، دقیق و حرفه‌ای)
            message_parts.append(f"📝 *ترجمه فارسی:*\n{translated_text}")

            # 3. پ.ن (کپشن) - توضیح ساده اصطلاحات مبهم
            footnote = self.create_educational_footnote(original_text, analysis_data)
            if footnote:
                message_parts.append(footnote)

            # 4. تحلیل سریع و کوتاه برای تریدرها (از analysis_data)
            if analysis_data.get('analysis'):
                message_parts.append(analysis_data['analysis'])

            # 5. آیدی کانال در انتها
            channel_id = getattr(self.config, 'CHANNEL_USERNAME', '@FinancialNewsFA')
            message_parts.append(f"\n📢 {channel_id}")

            # Join all parts
            message = '\n\n'.join(message_parts)

            # Apply Persian numbers and clean formatting
            message = self.format_numbers_persian(message)
            message = self.clean_markdown(message)

            # Ensure message is not too long for Telegram
            if len(message) > 4000:
                message = message[:3997] + "..."

            logger.info("Professional message formatted according to employer requirements")
            return message

        except Exception as e:
            logger.error(f"Error formatting message: {e}")
            # Fallback simple format
            return f"📝 *ترجمه:*\n{translated_text}\n\n📢 @FinancialNewsFA"

    def create_educational_footnote(self, original_text: str, analysis_data: Dict) -> str:
        """Create educational footnote explaining financial terms - EMPLOYER REQUIREMENTS"""
        try:
            financial_terms = []

            # Comprehensive financial terms dictionary for explanation
            term_explanations = {
                'FOMC': 'کمیته بازار باز فدرال - تصمیم‌گیرنده نرخ بهره آمریکا',
                'Hawkish': 'سیاست سخت‌گیرانه - احتمال افزایش نرخ بهره',
                'Dovish': 'سیاست نرم - احتمال کاهش نرخ بهره',
                'QE': 'تسهیل کمی - چاپ پول توسط بانک مرکزی برای تحریک اقتصاد',
                'QT': 'انقباض کمی - کاهش نقدینگی توسط بانک مرکزی',
                'GDP': 'تولید ناخالص داخلی - مجموع ارزش کالا و خدمات تولیدی',
                'CPI': 'شاخص قیمت مصرف‌کننده - اصلی‌ترین معیار تورم',
                'PPI': 'شاخص قیمت تولیدکننده - تورم در سطح تولید',
                'NFP': 'اشتغال غیرکشاورزی - شاخص کلیدی بازار کار آمریکا',
                'Fed': 'فدرال رزرو - بانک مرکزی آمریکا',
                'ECB': 'بانک مرکزی اروپا - تنظیم‌کننده سیاست پولی منطقه یورو',
                'BOE': 'بانک انگلستان - بانک مرکزی بریتانیا',
                'BOJ': 'بانک ژاپن - بانک مرکزی ژاپن',
                'DXY': 'شاخص دلار - قدرت دلار در برابر ۶ ارز اصلی',
                'VIX': 'شاخص ترس - معیار نوسانات بازار سهام',
                'Yield': 'بازده اوراق قرضه - نرخ سود اوراق دولتی',
                'Spread': 'اسپرد - اختلاف نرخ بهره بین دو ابزار مالی',
                'Tapering': 'کاهش تدریجی خرید اوراق توسط بانک مرکزی',
                'Recession': 'رکود - کاهش فعالیت اقتصادی برای دو فصل متوالی',
                'Bull Market': 'بازار صعودی - روند افزایشی قیمت‌ها',
                'Bear Market': 'بازار نزولی - کاهش ۲۰٪ یا بیشتر از اوج قیمت',
                'ETF': 'صندوق قابل معامله - سبد سهام قابل خرید مثل سهام عادی',
                'IPO': 'عرضه اولیه سهام - اولین فروش سهام شرکت به عموم'
            }

            # Find terms in original text (case insensitive)
            original_upper = original_text.upper()
            for term, explanation in term_explanations.items():
                if term.upper() in original_upper:
                    financial_terms.append(f"*{term}:* {explanation}")

            # Also check for complex financial situations that need explanation
            complex_situations = self.identify_complex_situations(original_text)
            financial_terms.extend(complex_situations)

            if financial_terms:
                footnote = "📌 *پ.ن (توضیح اصطلاحات):*\n" + "\n".join(financial_terms[:3])  # Max 3 explanations
                return footnote

            return ""

        except Exception as e:
            logger.error(f"Error creating footnote: {e}")
            return ""

    def identify_complex_situations(self, text: str) -> List[str]:
        """Identify complex financial situations that need explanation"""
        explanations = []
        text_lower = text.lower()

        # Complex economic situations
        if 'yield curve inversion' in text_lower or 'inverted yield' in text_lower:
            explanations.append("*منحنی معکوس بازده:* زمانی که اوراق کوتاه‌مدت بازده بالاتری از بلندمدت دارند - نشانه رکود")

        if 'quantitative tightening' in text_lower or 'balance sheet reduction' in text_lower:
            explanations.append("*انقباض کمی:* فروش اوراق بانک مرکزی برای کاهش نقدینگی بازار")

        if 'carry trade' in text_lower:
            explanations.append("*معامله حمل:* قرض گرفتن ارز کم‌بهره و سرمایه‌گذاری در ارز پربهره")

        if 'safe haven' in text_lower:
            explanations.append("*امن‌پناه:* دارایی‌هایی که در زمان بحران ارزش خود را حفظ می‌کنند (طلا، دلار)")

        return explanations

    def create_trading_analysis(self, analysis_data: Dict, original_text: str) -> str:
        """Create quick trading analysis for traders"""
        try:
            analysis_parts = []

            # Determine market impact based on keywords
            impact_analysis = self.analyze_market_impact(original_text)

            if impact_analysis:
                analysis_parts.append("📊 *تحلیل سریع برای تریدرها:*")

                # Add market impacts
                for market, impact in impact_analysis.items():
                    if impact:
                        analysis_parts.append(f"• *{market}:* {impact}")

            if analysis_parts:
                return "\n".join(analysis_parts)

            return ""

        except Exception as e:
            logger.error(f"Error creating trading analysis: {e}")
            return ""

    def analyze_market_impact(self, text: str) -> Dict[str, str]:
        """Analyze potential market impact based on text content"""
        try:
            text_lower = text.lower()
            impacts = {}

            # Dollar impact keywords
            if any(word in text_lower for word in ['fed', 'interest rate', 'fomc', 'powell', 'dollar']):
                if any(word in text_lower for word in ['raise', 'hike', 'increase', 'hawkish']):
                    impacts['دلار (DXY)'] = 'احتمال تقویت کوتاه‌مدت'
                elif any(word in text_lower for word in ['cut', 'lower', 'dovish', 'decrease']):
                    impacts['دلار (DXY)'] = 'احتمال تضعیف'

            # Gold impact
            if any(word in text_lower for word in ['inflation', 'gold', 'safe haven', 'uncertainty']):
                impacts['طلا (XAU/USD)'] = 'احتمال رشد در صورت عدم‌اطمینان'

            # Forex pairs
            if 'eur' in text_lower or 'euro' in text_lower:
                impacts['یورو/دلار'] = 'نیاز به پیگیری تحولات اروپا'

            # Crypto impact
            if any(word in text_lower for word in ['bitcoin', 'crypto', 'digital', 'regulation']):
                impacts['ارزهای دیجیتال'] = 'حساسیت بالا به اخبار نظارتی'

            # Oil impact
            if any(word in text_lower for word in ['oil', 'crude', 'opec', 'energy']):
                impacts['نفت (WTI/Brent)'] = 'تأثیر بر قیمت انرژی'

            # Stock indices
            if any(word in text_lower for word in ['stock', 'equity', 'market', 's&p', 'nasdaq']):
                impacts['شاخص‌ها'] = 'پیگیری واکنش بازارهای سهام'

            return impacts

        except Exception in e:
            logger.error(f"Error analyzing market impact: {e}")
            return {}

    def create_simple_message(self, original_text: str, translated_text: str, tweet_data: Dict) -> str:
        """Create a simple formatted message (fallback)"""
        try:
            news_emoji = self.emojis.get('news', '📰')
            globe_emoji = self.emojis.get('globe', '🌍')
            
            message = f"{news_emoji} *خبر مالی:*\n\n"
            message += f"{translated_text}\n\n"
            message += f"{globe_emoji} *منبع:* [@{tweet_data.get('username', 'Twitter')}]({tweet_data.get('url', '')})"
            
            return self.clean_markdown(message)
            
        except Exception as e:
            logger.error(f"Error creating simple message: {e}")
            return f"خبر جدید: {translated_text}"
    
    def format_error_message(self, error_type: str, original_text: str = "") -> str:
        """Format error message for debugging"""
        warning_emoji = self.emojis.get('warning', '⚠️')
        
        error_messages = {
            'translation_failed': 'خطا در ترجمه متن',
            'analysis_failed': 'خطا در تحلیل خبر',
            'formatting_failed': 'خطا در فرمت‌بندی پیام',
            'api_error': 'خطا در ارتباط با API'
        }
        
        error_msg = error_messages.get(error_type, 'خطای نامشخص')
        
        message = f"{warning_emoji} *خطا:* {error_msg}\n\n"
        if original_text:
            message += f"*متن اصلی:* {original_text[:200]}..."
        
        return message
