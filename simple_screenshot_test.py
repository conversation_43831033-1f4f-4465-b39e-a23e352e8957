"""
تست ساده اسکرین‌شات
"""
import asyncio
from telegram import Bot
from PIL import Image, ImageDraw, ImageFont
import io

async def test_simple_screenshot():
    """Test simple screenshot generation"""
    try:
        print("📸 Creating simple screenshot...")
        
        # Create simple image
        width, height = 600, 400
        img = Image.new('RGB', (width, height), color='white')
        draw = ImageDraw.Draw(img)
        
        # Draw border
        draw.rectangle([10, 10, width-10, height-10], outline='#1da1f2', width=3)
        
        # Add text
        try:
            font = ImageFont.truetype("arial.ttf", 16)
            title_font = ImageFont.truetype("arial.ttf", 20)
        except:
            font = ImageFont.load_default()
            title_font = ImageFont.load_default()
        
        # Draw content
        draw.text((30, 30), "📱 Tweet Screenshot", fill='#1da1f2', font=title_font)
        draw.text((30, 70), "Federal Reserve raises interest rates by 0.25%", fill='black', font=font)
        draw.text((30, 100), "to combat inflation concerns", fill='black', font=font)
        draw.text((30, 140), "This is a sample financial news tweet", fill='#666', font=font)
        draw.text((30, 170), "that would be translated and analyzed", fill='#666', font=font)
        draw.text((30, 220), "Source: @financialjuice", fill='#1da1f2', font=font)
        draw.text((30, 350), "Generated by Financial News Bot", fill='#999', font=font)
        
        # Convert to bytes
        img_buffer = io.BytesIO()
        img.save(img_buffer, format='PNG')
        img_buffer.seek(0)
        screenshot_data = img_buffer.getvalue()
        
        print("✅ Screenshot created!")
        
        # Send to Telegram
        print("📤 Sending to Telegram...")
        bot = Bot('**********************************************')
        
        caption = """📝 *ترجمه فارسی توییت:*
فدرال رزرو نرخ بهره را ۰.۲۵ درصد افزایش داد تا با تورم مبارزه کند

📌 *پ.ن:*
*Fed:* بانک مرکزی آمریکا

📊 *تحلیل سریع برای تریدرها:*
• *اقتصاد کلان:* تأثیر بر سیاست پولی آمریکا
• *بازارهای مالی:* تقویت احتمالی دلار

📢 @FinancialJuiceFarsi

🧪 *تست اسکرین‌شات کامل*"""
        
        message = await bot.send_photo(
            chat_id='@FinancialJuiceFarsi',
            photo=screenshot_data,
            caption=caption,
            parse_mode='Markdown'
        )
        
        print(f"✅ Screenshot post sent!")
        print(f"📱 Link: https://t.me/FinancialJuiceFarsi/{message.message_id}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    print("📸 SIMPLE SCREENSHOT TEST")
    print("=" * 40)
    
    result = asyncio.run(test_simple_screenshot())
    
    if result:
        print("\n🎉 Screenshot with image sent successfully!")
        print("📋 Check @FinancialJuiceFarsi to see the result")
    else:
        print("\n❌ Test failed")
