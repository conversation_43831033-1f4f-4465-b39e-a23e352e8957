"""
Test simple components without complex dependencies
"""
import sys
import os

# Add src directory to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

def test_simple_translation():
    """Test simple translation engine"""
    print("🔄 Testing Simple Translation Engine...")
    try:
        from config import get_config
        from src.simple_translation_engine import SimpleTranslationEngine
        
        config = get_config('production')
        engine = SimpleTranslationEngine(config)
        
        # Test translation
        sample_text = "BREAKING: Fed Chair Powell signals 0.25% rate hike at next FOMC meeting"
        translated = engine.translate_text(sample_text)
        
        print(f"Original: {sample_text}")
        print(f"Translated: {translated}")
        
        # Test confidence
        confidence = engine.get_translation_confidence(sample_text, translated)
        print(f"Confidence: {confidence}")
        
        return True
        
    except Exception as e:
        print(f"❌ Simple translation error: {e}")
        return False

def test_simple_analyzer():
    """Test simple news analyzer"""
    print("\n📊 Testing Simple News Analyzer...")
    try:
        from config import get_config
        from src.simple_news_analyzer import SimpleNewsAnalyzer
        
        config = get_config('production')
        analyzer = SimpleNewsAnalyzer(config)
        
        # Test analysis
        original_text = "Fed Chair <PERSON> signals hawkish stance, 0.25% rate hike expected at next FOMC meeting"
        translated_text = "رئیس فدرال رزرو پاول موضع سخت‌گیرانه اعلام کرد، افزایش ۰.۲۵% نرخ بهره در جلسه بعدی FOMC انتظار می‌رود"
        
        analysis_result = analyzer.analyze_news(original_text, translated_text)
        
        print(f"Sentiment: {analysis_result.get('sentiment', 'unknown')}")
        print(f"Confidence: {analysis_result.get('confidence', 0):.2f}")
        print("Analysis:")
        print(analysis_result.get('analysis', 'No analysis'))
        
        return True
        
    except Exception as e:
        print(f"❌ Simple analyzer error: {e}")
        return False

def test_message_formatter():
    """Test message formatter with simple components"""
    print("\n📝 Testing Message Formatter...")
    try:
        from config import get_config
        from src.message_formatter import MessageFormatter
        from datetime import datetime
        
        config = get_config('production')
        formatter = MessageFormatter(config)
        
        # Sample data
        original_text = "Fed raises rates 0.25%, Powell signals hawkish outlook for 2024"
        translated_text = "فدرال رزرو نرخ بهره را ۰.۲۵% افزایش داد، پاول چشم‌انداز سخت‌گیرانه برای ۲۰۲۴ اعلام کرد"
        
        analysis_data = {
            'analysis': '📊 *تحلیل سریع برای تریدرها:*\n• *فارکس (EUR/USD):* فشار نزولی - تقویت احتمالی دلار\n• *فارکس (GBP/USD):* احتمال کاهش - دلار قوی‌تر\n• *طلا (XAU/USD):* فشار نزولی - رقابت با دلار قوی\n• *شاخص‌ها (S&P 500):* احتمال رشد - بهبود چشم‌انداز\n\n*زمینه اقتصادی:* تصمیمات فدرال رزرو بر تمام بازارهای جهانی تأثیرگذار است',
            'sentiment': 'positive',
            'confidence': 0.85,
            'key_info': {
                'financial_terms': ['Fed', 'rates', 'Powell'],
                'market_symbols': []
            }
        }
        
        tweet_data = {
            'id': '123456789',
            'username': 'financialjuice',
            'url': 'https://twitter.com/financialjuice/status/123456789',
            'created_at': datetime.now()
        }
        
        # Format message
        formatted_message = formatter.create_professional_message(
            original_text, translated_text, analysis_data, tweet_data
        )
        
        print("Formatted Message:")
        print("=" * 60)
        print(formatted_message)
        print("=" * 60)
        
        return True
        
    except Exception as e:
        print(f"❌ Message formatter error: {e}")
        return False

def test_full_pipeline():
    """Test complete pipeline with simple components"""
    print("\n🔄 Testing Full Pipeline...")
    try:
        from config import get_config
        from src.simple_translation_engine import SimpleTranslationEngine
        from src.simple_news_analyzer import SimpleNewsAnalyzer
        from src.message_formatter import MessageFormatter
        from datetime import datetime
        
        config = get_config('production')
        
        # Initialize components
        translator = SimpleTranslationEngine(config)
        analyzer = SimpleNewsAnalyzer(config)
        formatter = MessageFormatter(config)
        
        # Sample tweet
        tweet_data = {
            'id': '1234567890',
            'text': 'BREAKING: Fed Chair Powell signals 0.25% rate hike at next FOMC meeting, citing persistent inflation concerns',
            'username': 'financialjuice',
            'url': 'https://twitter.com/financialjuice/status/1234567890',
            'created_at': datetime.now()
        }
        
        print("Processing sample tweet through pipeline...")
        print(f"Original: {tweet_data['text']}")
        
        # Step 1: Translation
        translated_text = translator.translate_text(tweet_data['text'])
        print(f"✅ Translated: {translated_text}")
        
        # Step 2: Analysis
        analysis_data = analyzer.analyze_news(tweet_data['text'], translated_text)
        print(f"✅ Analysis: {analysis_data.get('sentiment', 'unknown')} sentiment")
        
        # Step 3: Format message
        formatted_message = formatter.create_professional_message(
            tweet_data['text'], translated_text, analysis_data, tweet_data
        )
        print("✅ Message formatted")
        
        print("\n📋 FINAL RESULT:")
        print("=" * 70)
        print("📸 [اسکرین‌شات توییت - will be sent as image]")
        print()
        print(formatted_message)
        print("=" * 70)
        
        return True
        
    except Exception as e:
        print(f"❌ Full pipeline error: {e}")
        return False

def main():
    """Main test function"""
    print("🚀 Testing Simple Bot Components")
    print("Using simple implementations without complex dependencies")
    print("=" * 70)
    
    tests = [
        ("Simple Translation", test_simple_translation),
        ("Simple Analyzer", test_simple_analyzer),
        ("Message Formatter", test_message_formatter),
        ("Full Pipeline", test_full_pipeline)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n🔍 Running {test_name} test...")
        try:
            result = test_func()
            results.append(result)
            status = "✅ PASS" if result else "❌ FAIL"
            print(f"Result: {status}")
        except Exception as e:
            print(f"❌ Test {test_name} crashed: {e}")
            results.append(False)
    
    print("\n" + "=" * 70)
    print("📊 Test Results:")
    
    for i, (test_name, _) in enumerate(tests):
        status = "✅ PASS" if results[i] else "❌ FAIL"
        print(f"  {test_name}: {status}")
    
    passed = sum(results)
    total = len(results)
    
    print(f"\n🎯 Summary: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All simple components work!")
        print("\n📝 Next steps:")
        print("1. Test Telegram connection")
        print("2. Update main.py to use simple components")
        print("3. Run the bot!")
    else:
        print("⚠️ Some tests failed. Check errors above.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
