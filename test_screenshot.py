"""
تست اسکرین‌شات و ارسال با عکس
"""
import asyncio
import sys
import os

# Add src directory to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from telegram import Bo<PERSON>
from src.screenshot_generator import ScreenshotGenerator
from config import get_config

async def test_screenshot_post():
    """Test sending post with screenshot"""
    try:
        print("📸 Testing screenshot generation and sending...")
        
        # Initialize components
        config = get_config()
        screenshot_gen = ScreenshotGenerator(config)
        bot = Bot('8207363120:AAHL0OJoGCgehj3eQEF1Kh6HJmREKhe-vvw')
        
        # Sample tweet data
        tweet_text = "Federal Reserve raises interest rates by 0.25% to combat inflation"
        tweet_url = "https://twitter.com/financialjuice/status/123456"
        
        # Generate screenshot
        print("🔄 Generating screenshot...")
        screenshot_data = screenshot_gen.generate_tweet_screenshot(tweet_url, tweet_text)
        
        if not screenshot_data:
            print("⚠️ Screenshot failed, using placeholder...")
            screenshot_data = screenshot_gen.create_placeholder_image()
        
        # Create message
        message_text = """📝 *ترجمه فارسی توییت:*
فدرال رزرو نرخ بهره را ۰.۲۵ درصد افزایش داد تا با تورم مبارزه کند

📌 *پ.ن:*
*Fed:* بانک مرکزی آمریکا - تصمیم‌گیرنده سیاست پولی

📊 *تحلیل سریع برای تریدرها:*
تحلیل مختصر بر اساس اقتصاد کلان و خرد:
• *اقتصاد کلان:* تأثیر بر سیاست پولی آمریکا
• *بازارهای مالی:* تقویت احتمالی دلار، فشار بر طلا

📢 @FinancialJuiceFarsi

🧪 *تست اسکرین‌شات*"""
        
        # Send with photo
        print("📤 Sending post with screenshot...")
        
        if screenshot_data and len(screenshot_data) > 100:
            # Send as photo with caption
            message = await bot.send_photo(
                chat_id='@FinancialJuiceFarsi',
                photo=screenshot_data,
                caption=message_text,
                parse_mode='Markdown'
            )
            
            print(f"✅ Post with screenshot sent!")
            print(f"📱 Link: https://t.me/FinancialJuiceFarsi/{message.message_id}")
            
        else:
            # Fallback to text only
            message = await bot.send_message(
                chat_id='@FinancialJuiceFarsi',
                text=f"📸 *اسکرین‌شات توییت*\n\n{message_text}",
                parse_mode='Markdown'
            )
            
            print(f"✅ Text-only post sent (screenshot failed)")
            print(f"📱 Link: https://t.me/FinancialJuiceFarsi/{message.message_id}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    print("📸 SCREENSHOT TEST")
    print("=" * 40)
    
    result = asyncio.run(test_screenshot_post())
    
    if result:
        print("\n🎉 Screenshot test completed!")
        print("📋 Check the channel to see the result")
    else:
        print("\n❌ Screenshot test failed")
