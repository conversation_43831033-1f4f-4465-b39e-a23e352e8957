# ===============================================
# فایل تنظیمات ربات مترجم اخبار مالی
# این فایل را کپی کرده و نام آن را به .env تغییر دهید
# ===============================================

# ===== Twitter Configuration =====
# ✅ NO API REQUIRED - Uses FREE web scraping
# Twitter API keys are OPTIONAL (if you have them, uncomment below)
# TWITTER_BEARER_TOKEN=your_twitter_bearer_token_here
# TWITTER_API_KEY=your_twitter_api_key_here
# TWITTER_API_SECRET=your_twitter_api_secret_here
# TWITTER_ACCESS_TOKEN=your_twitter_access_token_here
# TWITTER_ACCESS_TOKEN_SECRET=your_twitter_access_token_secret_here

# ===== Telegram Bot Configuration =====
# ⚠️ REQUIRED: برای دریافت توکن ربات به @BotFather در تلگرام مراجعه کنید
TELEGRAM_BOT_TOKEN=**********************************************

# ⚠️ REQUIRED: آیدی کانال تلگرام (مثال: @your_channel یا -100xxxxxxxxx)
TELEGRAM_CHANNEL_ID=@FinancialJuiceFarsi

# ===== Target Twitter Account =====
# نام کاربری توییتر که می‌خواهید پایش شود (بدون @)
TARGET_TWITTER_USERNAME=financialjuice

# ===== FREE SERVICES ONLY - NO PAID APIs REQUIRED =====
# ✅ ربات از سرویس‌های رایگان استفاده می‌کند
# OpenAI API اختیاری است و نیازی به آن نیست

# ===== Optional Settings =====
# محیط اجرا (development یا production)
ENVIRONMENT=development

# نام کانال برای نمایش در انتهای پیام‌ها
CHANNEL_USERNAME=@FinancialJuiceFarsi
