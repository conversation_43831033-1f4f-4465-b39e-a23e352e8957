2025-08-11 18:58:32 | INFO     | __main__:setup_logging:72 - Logging setup completed
2025-08-11 18:58:32 | INFO     | __main__:start:258 - 🚀 Starting Financial News Translation Bot
2025-08-11 18:58:32 | INFO     | __main__:initialize_components:81 - Initializing bot components...
2025-08-11 18:58:32 | INFO     | src.twitter_bot:setup_session:40 - Web scraping session initialized - NO API REQUIRED
2025-08-11 18:58:32 | INFO     | src.twitter_bot:setup_database:64 - Database setup completed
2025-08-11 18:58:32 | INFO     | __main__:initialize_components:85 - ✓ Twitter bot initialized
2025-08-11 18:58:32 | INFO     | src.translation_engine:__init__:49 - Deep Translator (Google) initialized - FREE
2025-08-11 18:58:33 | INFO     | src.translation_engine:load_financial_terms:93 - Loaded 117 financial terms
2025-08-11 18:58:33 | INFO     | __main__:initialize_components:89 - ✓ Translation engine initialized
2025-08-11 18:58:33 | INFO     | src.news_analyzer:load_financial_terms:160 - Financial terms loaded for analysis
2025-08-11 18:58:33 | INFO     | __main__:initialize_components:93 - ✓ News analyzer initialized
2025-08-11 18:58:33 | INFO     | src.message_formatter:load_emojis:27 - Emojis loaded for message formatting
2025-08-11 18:58:33 | INFO     | __main__:initialize_components:97 - ✓ Message formatter initialized
2025-08-11 18:58:33 | INFO     | __main__:initialize_components:101 - ✓ Screenshot generator initialized
2025-08-11 19:03:38 | INFO     | __main__:setup_logging:72 - Logging setup completed
2025-08-11 19:03:38 | INFO     | __main__:start:258 - 🚀 Starting Financial News Translation Bot
2025-08-11 19:03:38 | INFO     | __main__:initialize_components:81 - Initializing bot components...
2025-08-11 19:03:39 | INFO     | src.twitter_bot:setup_session:40 - Web scraping session initialized - NO API REQUIRED
2025-08-11 19:03:39 | INFO     | src.twitter_bot:setup_database:64 - Database setup completed
2025-08-11 19:03:39 | INFO     | __main__:initialize_components:85 - ✓ Twitter bot initialized
2025-08-11 19:03:39 | INFO     | src.translation_engine:__init__:49 - Deep Translator (Google) initialized - FREE
2025-08-11 19:03:39 | INFO     | src.translation_engine:load_financial_terms:93 - Loaded 117 financial terms
2025-08-11 19:03:39 | INFO     | __main__:initialize_components:89 - ✓ Translation engine initialized
2025-08-11 19:03:39 | INFO     | src.news_analyzer:load_financial_terms:160 - Financial terms loaded for analysis
2025-08-11 19:03:39 | INFO     | __main__:initialize_components:93 - ✓ News analyzer initialized
2025-08-11 19:03:39 | INFO     | src.message_formatter:load_emojis:27 - Emojis loaded for message formatting
2025-08-11 19:03:39 | INFO     | __main__:initialize_components:97 - ✓ Message formatter initialized
2025-08-11 19:03:39 | INFO     | __main__:initialize_components:101 - ✓ Screenshot generator initialized
2025-08-11 19:04:18 | INFO     | src.telegram_bot:setup_bot:51 - Telegram bot initialized successfully
2025-08-11 19:04:23 | ERROR    | src.telegram_bot:test_connection:87 - Bot connection test failed: Timed out
2025-08-11 19:04:23 | ERROR    | __main__:initialize_components:115 - Failed to initialize components: Telegram bot connection failed
2025-08-11 19:04:24 | ERROR    | __main__:start:281 - Failed to start bot: Telegram bot connection failed
2025-08-11 19:04:24 | ERROR    | __main__:main:349 - Bot crashed: Telegram bot connection failed
2025-08-11 19:04:24 | INFO     | __main__:stop:286 - Stopping bot...
2025-08-11 19:04:25 | ERROR    | src.telegram_bot:send_message:106 - Send message error: Pool timeout: All connections in the connection pool are occupied. Request was *not* sent to Telegram. Consider adjusting the connection pool size or the pool timeout.
2025-08-11 19:04:25 | INFO     | __main__:stop:311 - Bot stopped successfully
