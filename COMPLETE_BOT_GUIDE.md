# 🤖 ربات کامل مترجم اخبار مالی

## 📋 خلاصه پروژه

این ربات **کاملاً رایگان** اخبار مالی را از توییتر @financialjuice دریافت کرده، ترجمه می‌کند و با تحلیل کامل به کانال تلگرام ارسال می‌کند.

## ✅ ویژگی‌های کلیدی

- **🆓 کاملاً رایگان** - بدون نیاز به API پولی
- **📝 ترجمه هوشمند** - ترجمه روان و دقیق (نه تحت‌اللفظی)
- **📌 توضیح اصطلاحات** - توضیح ساده مفاهیم مالی پیچیده
- **📊 تحلیل بازار** - تحلیل تأثیر اخبار بر بازارهای مختلف
- **🔄 پایش خودکار** - پایش مداوم و ارسال فوری
- **💾 پایگاه داده** - جلوگیری از تکرار پیام‌ها

## 🛠️ نصب و راه‌اندازی

### مرحله 1: نصب Python و کتابخانه‌ها

```bash
# نصب کتابخانه‌های مورد نیاز
pip install deep-translator beautifulsoup4 requests python-dotenv python-telegram-bot loguru
```

### مرحله 2: تنظیم ربات تلگرام

1. به @BotFather در تلگرام پیام دهید
2. دستور `/newbot` را ارسال کنید
3. نام و username ربات را انتخاب کنید
4. توکن دریافتی را کپی کنید

### مرحله 3: ایجاد کانال تلگرام

1. کانال جدید ایجاد کنید
2. ربات را به کانال اضافه کنید
3. ربات را Admin کنید
4. نام کانال (مثل @YourChannel) را یادداشت کنید

### مرحله 4: تنظیم فایل .env

فایل `sample.env` را به `.env` تغییر نام دهید و تنظیمات را وارد کنید:

```env
TELEGRAM_BOT_TOKEN=1234567890:ABCdefGHIjklMNOpqrsTUVwxyz
TELEGRAM_CHANNEL_ID=@YourChannelName
TARGET_TWITTER_USERNAME=financialjuice
ENVIRONMENT=production
```

### مرحله 5: اجرای ربات

```bash
python complete_financial_bot.py
```

## 📱 فرمت پیام‌ها

ربات طبق دستورالعمل دقیق کارفرما پیام‌ها را به این شکل فرمت می‌کند:

```
📝 **ترجمه:**
[ترجمه روان و دقیق فارسی]

📌 **پ.ن (توضیح اصطلاحات):**
**FOMC:** کمیته بازار باز فدرال - تصمیم‌گیرنده نرخ بهره آمریکا

📊 **تحلیل کوتاه:**
**اوراق قرضه:** تغییر انتظارات نرخ بهره
**دلار:** تأثیر بر قدرت ارز
**شاخص‌ها:** تأثیر بر انتظارات رشد

📢 @FinancialJuiceFarsi
```

## 🎯 تحلیل بازارهای مالی

ربات تأثیر اخبار را بر این بازارها تحلیل می‌کند:

- **📈 فارکس** - تأثیر بر ارزهای مختلف
- **🥇 طلا** - حفاظت در برابر تورم
- **📊 شاخص‌ها** - S&P 500, Dow Jones, Nasdaq
- **📋 اوراق قرضه** - تغییر بازده‌ها
- **🛢️ نفت** - تأثیر بر قیمت انرژی
- **₿ کریپتو** - Bitcoin و ارزهای دیجیتال

## 🔧 عیب‌یابی

### مشکلات رایج:

1. **خطای اتصال تلگرام:**
   - توکن ربات را بررسی کنید
   - ربات را Admin کانال کنید

2. **عدم دریافت توییت:**
   - اتصال اینترنت را بررسی کنید
   - ممکن است nitter.net در دسترس نباشد

3. **خطای ترجمه:**
   - deep-translator نصب باشد
   - اتصال اینترنت فعال باشد

## 📞 پشتیبانی

در صورت بروز مشکل:
1. لاگ‌های ربات را بررسی کنید
2. فایل .env را دوباره چک کنید
3. کتابخانه‌ها را دوباره نصب کنید

## 💡 نکات مهم

- ربات **کاملاً رایگان** است و هیچ هزینه اضافی ندارد
- از سرویس‌های رایگان Google Translate استفاده می‌کند
- اسکرپ کردن توییتر بدون نیاز به API انجام می‌شود
- تمام تحلیل‌ها بر پایه قوانین از پیش تعریف شده است

---
**توسعه‌دهنده:** Augment Agent  
**تاریخ:** 2025-08-11  
**نسخه:** 1.0 - Complete Edition
