"""
Screenshot Generator Module for Financial News Translation Bot
Generates screenshots of tweets for Telegram posts
"""
import requests
import io
import base64
from typing import Optional
from loguru import logger
from config import Config

class ScreenshotGenerator:
    """Generate screenshots of tweets"""
    
    def __init__(self, config: Config):
        self.config = config
        self.session = requests.Session()
        self.setup_session()
    
    def setup_session(self):
        """Setup session for screenshot requests"""
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
    
    def generate_tweet_screenshot(self, tweet_url: str, tweet_text: str) -> Optional[bytes]:
        """Generate screenshot of tweet"""
        try:
            logger.info(f"Generating screenshot for: {tweet_url}")
            
            # Method 1: Try screenshot API service
            screenshot_data = self.get_screenshot_from_api(tweet_url)
            if screenshot_data:
                return screenshot_data
            
            # Method 2: Try alternative screenshot service
            screenshot_data = self.get_screenshot_alternative(tweet_url)
            if screenshot_data:
                return screenshot_data
            
            # Method 3: Generate text-based image
            screenshot_data = self.generate_text_image(tweet_text, tweet_url)
            if screenshot_data:
                return screenshot_data
            
            logger.warning("All screenshot methods failed")
            return None
            
        except Exception as e:
            logger.error(f"Error generating screenshot: {e}")
            return None
    
    def get_screenshot_from_api(self, tweet_url: str) -> Optional[bytes]:
        """Get screenshot using free API service"""
        try:
            # Using free screenshot API
            api_url = f"https://api.screenshotmachine.com"
            params = {
                'key': 'demo',  # Free demo key
                'url': tweet_url,
                'dimension': '1024x768',
                'format': 'png'
            }
            
            response = self.session.get(api_url, params=params, timeout=15)
            if response.status_code == 200 and len(response.content) > 1000:
                logger.info("Screenshot generated via API")
                return response.content
            
            return None
            
        except Exception as e:
            logger.warning(f"API screenshot failed: {e}")
            return None
    
    def get_screenshot_alternative(self, tweet_url: str) -> Optional[bytes]:
        """Get screenshot using alternative service"""
        try:
            # Using htmlcsstoimage.com free tier
            api_url = "https://hcti.io/v1/image"
            
            html_content = f"""
            <div style="font-family: Arial; padding: 20px; background: white; border: 1px solid #ccc; border-radius: 10px; max-width: 500px;">
                <div style="color: #1da1f2; font-weight: bold; margin-bottom: 10px;">📱 Tweet Screenshot</div>
                <div style="color: #333; line-height: 1.4;">
                    Content from: {tweet_url}
                </div>
                <div style="color: #666; font-size: 12px; margin-top: 10px;">
                    Generated by Financial News Bot
                </div>
            </div>
            """
            
            data = {
                'html': html_content,
                'css': 'body { margin: 0; }',
                'google_fonts': 'Arial'
            }
            
            # This would need API key for real use, but shows the concept
            logger.info("Alternative screenshot method attempted")
            return None
            
        except Exception as e:
            logger.warning(f"Alternative screenshot failed: {e}")
            return None
    
    def generate_text_image(self, tweet_text: str, tweet_url: str) -> Optional[bytes]:
        """Generate a simple text-based image as fallback"""
        try:
            # Create a simple text-based "screenshot" using PIL
            from PIL import Image, ImageDraw, ImageFont
            
            # Create image
            width, height = 600, 400
            img = Image.new('RGB', (width, height), color='white')
            draw = ImageDraw.Draw(img)
            
            # Try to use default font
            try:
                font = ImageFont.truetype("arial.ttf", 16)
                title_font = ImageFont.truetype("arial.ttf", 18)
            except:
                font = ImageFont.load_default()
                title_font = ImageFont.load_default()
            
            # Draw border
            draw.rectangle([10, 10, width-10, height-10], outline='#1da1f2', width=2)
            
            # Draw title
            draw.text((20, 20), "📱 Tweet Content", fill='#1da1f2', font=title_font)
            
            # Draw tweet text (wrapped)
            lines = self.wrap_text(tweet_text, 70)
            y_pos = 60
            for line in lines[:15]:  # Max 15 lines
                draw.text((20, y_pos), line, fill='black', font=font)
                y_pos += 20
            
            # Draw URL
            draw.text((20, height-40), f"Source: {tweet_url}", fill='#666', font=font)
            
            # Convert to bytes
            img_buffer = io.BytesIO()
            img.save(img_buffer, format='PNG')
            img_buffer.seek(0)
            
            logger.info("Text-based screenshot generated")
            return img_buffer.getvalue()
            
        except ImportError:
            logger.warning("PIL not available for text image generation")
            return None
        except Exception as e:
            logger.warning(f"Text image generation failed: {e}")
            return None
    
    def wrap_text(self, text: str, width: int) -> list:
        """Wrap text to specified width"""
        words = text.split()
        lines = []
        current_line = []
        current_length = 0
        
        for word in words:
            if current_length + len(word) + 1 <= width:
                current_line.append(word)
                current_length += len(word) + 1
            else:
                if current_line:
                    lines.append(' '.join(current_line))
                current_line = [word]
                current_length = len(word)
        
        if current_line:
            lines.append(' '.join(current_line))
        
        return lines
    
    def create_placeholder_image(self) -> bytes:
        """Create a placeholder image when screenshot fails"""
        try:
            from PIL import Image, ImageDraw, ImageFont
            
            width, height = 600, 300
            img = Image.new('RGB', (width, height), color='#f0f0f0')
            draw = ImageDraw.Draw(img)
            
            # Draw placeholder text
            text = "📸 Tweet Screenshot\n\nContent will be displayed here\nwhen screenshot is available"
            
            try:
                font = ImageFont.truetype("arial.ttf", 20)
            except:
                font = ImageFont.load_default()
            
            # Center text
            bbox = draw.textbbox((0, 0), text, font=font)
            text_width = bbox[2] - bbox[0]
            text_height = bbox[3] - bbox[1]
            
            x = (width - text_width) // 2
            y = (height - text_height) // 2
            
            draw.text((x, y), text, fill='#666', font=font, align='center')
            
            # Convert to bytes
            img_buffer = io.BytesIO()
            img.save(img_buffer, format='PNG')
            img_buffer.seek(0)
            
            return img_buffer.getvalue()
            
        except ImportError:
            # Return a minimal placeholder if PIL not available
            return b'PNG_PLACEHOLDER_DATA'
        except Exception as e:
            logger.error(f"Error creating placeholder: {e}")
            return b'PNG_PLACEHOLDER_DATA'
