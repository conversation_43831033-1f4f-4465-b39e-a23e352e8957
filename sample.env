# ===============================================
# 🤖 فایل تنظیمات ربات مترجم اخبار مالی
# ===============================================

# ===== Telegram Bot Configuration =====
# ⚠️ ضروری: توکن ربات تلگرام (از @BotFather دریافت کنید)
TELEGRAM_BOT_TOKEN=your_telegram_bot_token_here

# ⚠️ ضروری: آیدی کانال تلگرام (مثل @YourChannelName یا -100xxxxxxxxx)
TELEGRAM_CHANNEL_ID=@YourChannelName

# ===== Target Twitter Account =====
# نام کاربری توییتر که می‌خواهید پایش شود (بدون @)
# پیشنهادی: financialjuice (اخبار مالی و اقتصادی)
# گزینه‌های دیگر: zerohedge, business, reuters, bloomberg
TARGET_TWITTER_USERNAME=financialjuice

# ===== Optional Settings =====
# محیط اجرا (development یا production)
ENVIRONMENT=production

# ===============================================
# 📋 راهنمای راه‌اندازی:
# ===============================================
# 
# 1. نام فایل را به .env تغییر دهید
# 2. TELEGRAM_BOT_TOKEN را از @BotFather دریافت کنید
# 3. TELEGRAM_CHANNEL_ID را وارد کنید
# 4. ربات را به کانال اضافه کنید و Admin کنید
# 5. python complete_financial_bot.py را اجرا کنید
#
# ✅ هیچ هزینه اضافی ندارد - کاملاً رایگان!
# ===============================================
